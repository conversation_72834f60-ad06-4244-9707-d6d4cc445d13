
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for bonito-fe-csr/src/components/comprehensive-analysis/hooks/use-ai-response.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">bonito-fe-csr/src/components/comprehensive-analysis/hooks</a> use-ai-response.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/44</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/44</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import {<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
  sampleVolumeData,
  sampleSourceData,
  sampleSentimentData,
  sampleWordData,
  sampleTopPosts,
} from "../constants";
&nbsp;
<span class="cstat-no" title="statement not covered" >export function useAIResponse() {</span>
<span class="cstat-no" title="statement not covered" >  const shouldIncludeCharts = (userMessage: string): boolean =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const chartKeywords = [</span>
<span class="cstat-no" title="statement not covered" >      "聲量",</span>
<span class="cstat-no" title="statement not covered" >      "趨勢",</span>
<span class="cstat-no" title="statement not covered" >      "分析",</span>
<span class="cstat-no" title="statement not covered" >      "統計",</span>
<span class="cstat-no" title="statement not covered" >      "數據",</span>
<span class="cstat-no" title="statement not covered" >      "圖表",</span>
<span class="cstat-no" title="statement not covered" >      "報告",</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
<span class="cstat-no" title="statement not covered" >    return chartKeywords.some((keyword) =&gt; userMessage.includes(keyword));</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const generateAIResponse = (userMessage: string): string =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    // Simple response generation based on keywords</span>
<span class="cstat-no" title="statement not covered" >    if (userMessage.includes("聲量") || userMessage.includes("趨勢")) {</span>
<span class="cstat-no" title="statement not covered" >      return "根據最新數據分析，該品牌在過去一週的討論聲量呈現穩定上升趨勢，總提及次數為3,247次，較前週增長18%。主要討論平台分布：Facebook 42%、Instagram 28%、Twitter 20%、其他平台 10%。整體情緒傾向正面，正面情緒佔65%，中性25%，負面10%。\n\n以下是詳細的分析圖表：";</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (userMessage.includes("抱怨") || userMessage.includes("負面")) {</span>
<span class="cstat-no" title="statement not covered" >      return "分析負面評價內容，主要抱怨點集中在以下幾個方面：1. 服務品質問題（32%）2. 產品功能不符預期（28%）3. 價格偏高（22%）4. 客服響應速度（18%）。建議重點關注服務品質改善和產品功能優化。\n\n以下是相關的分析圖表：";</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    if (userMessage.includes("競品")) {</span>
<span class="cstat-no" title="statement not covered" >      return "競品分析顯示，主要競爭對手在社群媒體上的活躍度有所提升，特別是在產品宣傳和用戶互動方面。建議加強我們的社群經營策略，提高用戶參與度。\n\n以下是競品對比分析：";</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return "我已經分析了相關數據，根據輿情監測結果，為您提供以下洞察和建議。如需更詳細的分析報告，請告訴我您想深入了解的特定方面。\n\n以下是分析結果的視覺化呈現：";</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const getChartsForMessage = (userMessage: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    return shouldIncludeCharts(userMessage)</span>
<span class="cstat-no" title="statement not covered" >      ? {</span>
<span class="cstat-no" title="statement not covered" >          volumeTrend: sampleVolumeData,</span>
<span class="cstat-no" title="statement not covered" >          sourceDistribution: sampleSourceData,</span>
<span class="cstat-no" title="statement not covered" >          sentiment: sampleSentimentData,</span>
<span class="cstat-no" title="statement not covered" >          wordCloud: sampleWordData,</span>
<span class="cstat-no" title="statement not covered" >          topPosts: sampleTopPosts,</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >      : undefined;</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return {</span>
<span class="cstat-no" title="statement not covered" >    shouldIncludeCharts,</span>
<span class="cstat-no" title="statement not covered" >    generateAIResponse,</span>
<span class="cstat-no" title="statement not covered" >    getChartsForMessage,</span>
<span class="cstat-no" title="statement not covered" >  };</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-29T11:17:38.786Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    