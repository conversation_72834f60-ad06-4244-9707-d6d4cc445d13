
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for bonito-fe-csr/src/components/topic-analysis/constants.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">bonito-fe-csr/src/components/topic-analysis</a> constants.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/94</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/94</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import type { AnalysisRecord } from "./types";
&nbsp;
<span class="cstat-no" title="statement not covered" >export const suggestedTopics = [</span>
<span class="cstat-no" title="statement not covered" >  "iPhone 15 Pro Max",</span>
<span class="cstat-no" title="statement not covered" >  "Samsung Galaxy S25",</span>
<span class="cstat-no" title="statement not covered" >  "Google Pixel 9",</span>
<span class="cstat-no" title="statement not covered" >  "小米 14 Ultra",</span>
<span class="cstat-no" title="statement not covered" >  "OPPO Find X7",</span>
<span class="cstat-no" title="statement not covered" >  "Vivo X100 Pro",</span>
<span class="cstat-no" title="statement not covered" >  "華為 Mate 60 Pro",</span>
<span class="cstat-no" title="statement not covered" >  "Sony Xperia 1 VI",</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const keywordGroups = [</span>
<span class="cstat-no" title="statement not covered" >  { id: 1, name: "性能" },</span>
<span class="cstat-no" title="statement not covered" >  { id: 2, name: "價格" },</span>
<span class="cstat-no" title="statement not covered" >  { id: 3, name: "設計" },</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >// 初始分析記錄</span>
<span class="cstat-no" title="statement not covered" >export const initialAnalysisRecords: AnalysisRecord[] = [</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    id: "1",</span>
<span class="cstat-no" title="statement not covered" >    query: "黃建賓",</span>
<span class="cstat-no" title="statement not covered" >    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),</span>
<span class="cstat-no" title="statement not covered" >    resultCount: 30927,</span>
<span class="cstat-no" title="statement not covered" >    status: "completed",</span>
<span class="cstat-no" title="statement not covered" >    summary: "政治相關討論為主，情緒好感度為0.34",</span>
<span class="cstat-no" title="statement not covered" >    clusters: ["3"],</span>
<span class="cstat-no" title="statement not covered" >    keywordGroups: [1, 3],</span>
<span class="cstat-no" title="statement not covered" >    dateRange: {</span>
<span class="cstat-no" title="statement not covered" >      start: "2024-06-01",</span>
<span class="cstat-no" title="statement not covered" >      end: "2025-05-31",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    selectedSources: {</span>
<span class="cstat-no" title="statement not covered" >      "3": ["ptt", "dcard"],</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    selectedBoards: {</span>
<span class="cstat-no" title="statement not covered" >      ptt: ["gossiping", "hatepolitics"],</span>
<span class="cstat-no" title="statement not covered" >      dcard: ["mood", "funny"],</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    id: "2",</span>
<span class="cstat-no" title="statement not covered" >    query: "Samsung Galaxy S25",</span>
<span class="cstat-no" title="statement not covered" >    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),</span>
<span class="cstat-no" title="statement not covered" >    resultCount: 3187,</span>
<span class="cstat-no" title="statement not covered" >    status: "completed",</span>
<span class="cstat-no" title="statement not covered" >    summary: "相機功能受到好評，充電速度有待改善",</span>
<span class="cstat-no" title="statement not covered" >    clusters: ["2", "3"],</span>
<span class="cstat-no" title="statement not covered" >    keywordGroups: [2, 4],</span>
<span class="cstat-no" title="statement not covered" >    dateRange: {</span>
<span class="cstat-no" title="statement not covered" >      start: "2025-01-10",</span>
<span class="cstat-no" title="statement not covered" >      end: "2025-01-20",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    selectedSources: {</span>
<span class="cstat-no" title="statement not covered" >      "2": ["facebook", "instagram"],</span>
<span class="cstat-no" title="statement not covered" >      "3": ["ptt", "mobile01"],</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    selectedBoards: {</span>
<span class="cstat-no" title="statement not covered" >      ptt: ["c_chat", "tech_job"],</span>
<span class="cstat-no" title="statement not covered" >      mobile01: ["smartphone", "camera"],</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    id: "3",</span>
<span class="cstat-no" title="statement not covered" >    query: "Google Pixel 9",</span>
<span class="cstat-no" title="statement not covered" >    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),</span>
<span class="cstat-no" title="statement not covered" >    resultCount: 1845,</span>
<span class="cstat-no" title="statement not covered" >    status: "completed",</span>
<span class="cstat-no" title="statement not covered" >    summary: "AI功能創新獲得認可，但市場可見度不足",</span>
<span class="cstat-no" title="statement not covered" >    clusters: ["1", "4"],</span>
<span class="cstat-no" title="statement not covered" >    keywordGroups: [1],</span>
<span class="cstat-no" title="statement not covered" >    dateRange: {</span>
<span class="cstat-no" title="statement not covered" >      start: "2025-01-05",</span>
<span class="cstat-no" title="statement not covered" >      end: "2025-01-18",</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >    selectedSources: {</span>
<span class="cstat-no" title="statement not covered" >      "1": ["udn", "ettoday"],</span>
<span class="cstat-no" title="statement not covered" >      "4": ["google_review", "app_store"],</span>
<span class="cstat-no" title="statement not covered" >    },</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const statusColorMap = {</span>
<span class="cstat-no" title="statement not covered" >  completed: "bg-green-100 text-green-800",</span>
<span class="cstat-no" title="statement not covered" >  analyzing: "bg-blue-100 text-blue-800",</span>
<span class="cstat-no" title="statement not covered" >  failed: "bg-red-100 text-red-800",</span>
<span class="cstat-no" title="statement not covered" >  default: "bg-gray-100 text-gray-800",</span>
<span class="cstat-no" title="statement not covered" >} as const;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >// Status text is now handled by i18n translations in topicAnalysis.status</span>
<span class="cstat-no" title="statement not covered" >// Removed statusTextMap - use translations instead</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const summaryTemplates = [</span>
<span class="cstat-no" title="statement not covered" >  "整體評價正面，但價格成為主要討論焦點",</span>
<span class="cstat-no" title="statement not covered" >  "功能創新獲得認可，使用體驗有待改善",</span>
<span class="cstat-no" title="statement not covered" >  "性價比優勢明顯，品牌認知度需要提升",</span>
<span class="cstat-no" title="statement not covered" >  "設計美學受到讚賞，技術規格表現亮眼",</span>
<span class="cstat-no" title="statement not covered" >  "市場反應熱烈，部分功能仍需優化",</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-29T11:17:38.786Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    