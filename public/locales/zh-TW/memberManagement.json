{"title": "成員管理", "serviceMembers": "服務成員：10/10", "addMember": "新增成員帳號(10)", "user": "使用者", "role": "角色", "email": "信箱", "permissions": "管理權組", "actions": "操作", "permissionSettings": "權限設定", "confirmDeleteTitle": "刪除使用者", "confirmDeleteMessage": "您確定要刪除使用者「{{username}}」嗎？此操作無法復原。", "createUser": {"title": "建立新使用者帳號", "editTitle": "編輯使用者帳號", "success": {"title": "帳號建立成功！", "editTitle": "帳號更新成功！", "message": "使用者帳號已成功建立！", "editMessage": "使用者帳號已成功更新！"}, "fields": {"name": "使用者名稱", "namePlaceholder": "請輸入使用者名稱", "email": "電子郵件", "emailPlaceholder": "請輸入電子郵件地址", "password": "密碼", "passwordPlaceholder": "請輸入密碼", "confirmPassword": "確認密碼", "confirmPasswordPlaceholder": "請再次輸入密碼", "role": "角色"}, "roles": {"admin": "管理員", "manager": "一般使用者"}, "buttons": {"cancel": "取消", "create": "建立帳號", "update": "更新帳號", "creating": "創建中...", "updating": "更新中..."}, "validation": {"name": {"minLength": "使用者名稱至少需要 2 個字元", "maxLength": "使用者名稱不能超過 50 個字元"}, "email": {"invalid": "請輸入有效的電子郵件地址", "taken": "此電子郵件地址已被使用", "required": "需要電子郵件"}, "password": {"minLength": "密碼至少需要 8 個字元", "letter": "密碼必須包含至少一個字母", "number": "密碼必須包含至少一個數字", "specialChar": "密碼必須包含至少一個特殊字元", "uppercase": "密碼必須至少具有一個大寫字符", "lowercase": "密碼必須至少具有一個小寫字符"}, "role": "請選擇使用者角色", "username": {"minLength": "用戶名必須至少3個字符長", "maxLength": "用戶名不得超過50個字符", "invalidCharacters": "用戶名只能包含字母，數字和下劃線"}, "firstName": {"required": "需要名字"}, "lastName": {"required": "需要姓氏"}}, "errors": {"roleNotFound": "找不到角色"}}, "description": "管理用戶及其在系統中的角色", "addUser": "添加用戶", "name": "姓名", "password": "密碼", "selectRole": "選擇角色", "success": {"userUpdated": "用戶成功更新了"}, "errors": {"updateFailed": "用戶更新失敗"}, "enterName": "輸入名稱", "enterEmail": "輸入電子郵件", "enterPassword": "輸入密碼", "username": "使用者名稱", "enterUsername": "輸入用戶名", "firstName": "名", "enterFirstName": "請輸入您的名字", "lastName": "姓", "enterLastName": "輸入最後名", "updateUser": "更新用戶"}