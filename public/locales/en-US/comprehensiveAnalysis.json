{"title": "Comprehensive Analysis", "subtitle": "In-depth public opinion analysis through AI conversation", "chatHistory": "Chat History", "newChat": "New Chat", "suggestedPrompts": "Suggested Questions", "inputPlaceholder": "Please enter your question...", "continuePlaceholder": "Continue asking or ask for details...", "send": "Send", "analyzing": "Analyzing...", "noHistory": "No chat history", "startFirstChat": "Start your first conversation", "aiAssistant": "AI Public Opinion Analysis Assistant", "features": {"trendAnalysis": "Trend Analysis", "trendAnalysisDesc": "Analyze brand voice trend changes", "anomalyDetection": "Anomaly Detection", "anomalyDetectionDesc": "Identify abnormal changes in public opinion", "deepInsights": "Deep Insights", "deepInsightsDesc": "Get professional analysis recommendations"}, "prompts": {"brandTrend": "How is the discussion volume trend for brand X in the past week?", "productComplaints": "Are there any main complaints about product Y?", "competitorSentiment": "How is the recent public opinion trend for competitor Z?", "activePlatforms": "Which platforms are most active in discussing our brand?", "negativeReviews": "Are there any recent negative reviews that need attention?", "productReaction": "How do users react to new product features?", "socialSentiment": "Brand mention sentiment analysis on social media", "hotTopics": "What are the hot discussion topics this month?"}}