{"title": "Role Management", "description": "Description", "addRole": "Add Role", "editRole": "Edit Role", "deleteRole": "Delete Role", "name": "Name", "permissions": "Permissions", "members": "Members", "createdAt": "Created At", "actions": "Actions", "confirmDeleteTitle": "Delete Role", "confirmDeleteMessage": "Are you sure you want to delete the role '{{name}}'? This action cannot be undone.", "delete": "Delete", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "roleName": "Role Name", "roleDescription": "Role Description", "selectPermissions": "Select Permissions", "addPermission": "Add Permission", "selectPermission": "Select a permission", "permissionName": "Permission Name", "action": "Action", "noPermissions": "No permissions added yet.", "permissionsList": {"users:read": "View users", "users:write": "Manage users", "roles:read": "View roles", "roles:write": "Manage roles", "reports:read": "View reports", "settings:read": "View settings", "settings:write": "Manage settings"}, "validation": {"nameRequired": "Role name is required", "nameMinLength": "Role name must be at least 3 characters", "nameMaxLength": "Role name cannot exceed 50 characters", "descriptionMaxLength": "Description cannot exceed 255 characters", "permissionsRequired": "At least one permission is required"}, "success": {"created": "Role created successfully", "updated": "Role updated successfully", "deleted": "Role deleted successfully"}, "error": {"createFailed": "Failed to create role", "updateFailed": "Failed to update role", "deleteFailed": "Failed to delete role"}, "updateRole": "Update Role", "createRole": "Create Role", "noPermissionsAvailable": "No permissions available", "back": "Back", "createRoleDescription": "Create role for user", "loadingPermissions": "Loading permissions", "loading": "loading", "roleNotFound": "Role not found", "editRoleDescription": "Edit role", "errors": {"deleteFailed": "Delete failed"}, "more": "more"}