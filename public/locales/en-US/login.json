{"title": "<PERSON><PERSON>", "fields": {"usernameOrEmail": "Username or Email", "password": "Password"}, "login": "<PERSON><PERSON>", "register": "Register", "loginError": "<PERSON><PERSON> failed. Please check your email and password.", "emailRequired": "Email is required.", "passwordRequired": "Password is required.", "passwordMinLength": "Password must be at least 6 characters long.", "passwordMaxLength": "Password cannot exceed 20 characters.", "status": {"logging": "Logging in..."}, "error": {"requiredFields": "Please fill in all required fields.", "invalidPassword": "Password must be at least {{minLength}} characters long, contain at least one uppercase letter, one lowercase letter, one number, and one special character.", "invalidCredentials": "Invalid username or password.", "userNotFound": "User not found.", "serverError": "Server error. Please try again later.", "loginFailed": "<PERSON><PERSON> failed. Please try again."}}