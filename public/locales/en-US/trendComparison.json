{"title": "Trend Comparison Analysis", "subtitle": "Compare volume trends of different topics across different time periods", "startAnalysis": "Start Analysis", "analyzing": "Analyzing...", "selectTopics": "Select Comparison Topics", "selectTimeRange": "Select Time Range", "selectCluster": "Select Data Cluster", "period1": "Period 1", "period2": "Period 2", "volumeComparison": "Volume Comparison", "trendChart": "Trend Chart", "comparisonResults": "Comparison Results", "timeUnit": "Time Unit", "day": "Day", "week": "Week", "month": "Month", "showDataPoints": "Show Data Points", "showAverageLine": "Show Average Line", "average": "Average", "analysisResults": "Analysis Results", "topicsCount": "topics", "clustersCount": "clusters", "requirements": {"title": "Please complete the following settings before starting analysis:", "minTopics": "Select at least 2 comparison topics", "maxTopics": "Select at most 5 comparison topics", "period1Required": "Set time period 1", "clustersRequired": "Select at least one data cluster"}, "topicNames": {"electricVehicles": "Electric Vehicle Market Trends", "artificialIntelligence": "AI Artificial Intelligence Development", "housingPolicy": "Housing Policy Discussion", "postPandemicRecovery": "Post-Pandemic Economic Recovery", "greenEnergyPolicy": "Green Energy Policy Promotion", "digitalTransformation": "Digital Transformation Trends"}, "charts": {"period1Title": "Period 1 Trend Chart", "period2Title": "Period 2 Trend Chart", "volumeComparisonTitle": "Volume Comparison Chart", "noDataMessage": "No data available", "chartOptions": "Chart Options", "toggleDataPoints": "Toggle data points display", "toggleAverageLine": "Toggle average line display", "timeUnitSelector": "Time unit selector", "volumeBarChart": "Volume Statistics Bar Chart", "volumePieChart": "Volume Statistics Pie Chart", "trendComparisonChart": "Volume Trend Comparison Chart", "showDataPoints": "Show Data Points", "averageLine": "Average Line", "period1Label": "Period 1", "period2Label": "Period 2", "period1Description": "Data from time period 1", "period2Description": "Data from time period 2"}}