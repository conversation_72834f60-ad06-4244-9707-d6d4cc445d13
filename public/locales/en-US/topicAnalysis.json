{"title": "Topic Analysis", "subtitle": "In-depth analysis of public opinion on specific topics", "description": "Enter keywords to deeply analyze the focus, pros and cons, and sentiment trends of specific topics", "newAnalysis": "New Analysis", "searchRecords": "Search analysis records...", "noRecords": "No analysis records yet", "startFirstAnalysis": "Start your first topic analysis", "hotTopics": "Hot Topics", "inputPlaceholder": "Enter topic keywords, e.g.: <PERSON>", "analyze": "Analyze", "analyzing": "Analyzing...", "selectCluster": "Select Data Cluster", "selectKeywords": "Select Keyword Groups", "selectSources": "Select Data Sources", "selectBoards": "Select Discussion Boards", "dateRange": "Date Range", "analysisResults": "Analysis Results", "summary": "Analysis Summary", "mainFindings": "Main Findings", "volumeTrend": "Volume Trend", "sourceDistribution": "Source Distribution", "sentimentAnalysis": "Sentiment Analysis", "wordCloud": "Keyword Cloud", "hotTopicsAnalysis": "Hot Topics", "topPosts": "Top Posts", "positiveEmotion": "More positive emotions", "negativeEmotion": "More negative emotions", "features": {"discussionFocus": "Discussion Focus Analysis", "discussionFocusDesc": "Discover topics that netizens care about most", "sentimentTracking": "Sentiment Trend Tracking", "sentimentTrackingDesc": "Track emotional change trends", "hotArticles": "Hot Articles Analysis", "hotArticlesDesc": "Get representative article content"}, "analysisRecord": {"recordsUnit": "records", "sources": "sources", "boards": "boards"}, "status": {"completed": "Completed", "analyzing": "Analyzing", "failed": "Failed", "unknown": "Unknown"}, "messages": {"analyzingInProgress": "Analyzing in progress..."}, "results": {"header": {"back": "Back", "analysisResults": "Analysis Results", "analysisTime": "Analysis Time", "totalVolume": "Total Volume", "recordsUnit": "records", "analysisCompleted": "Analysis Completed", "share": "Share", "exportReport": "Export Report"}, "tabs": {"overview": "Overview", "sentiment": "Sentiment Analysis", "topics": "Topics Analysis", "sources": "Sources Analysis"}, "keyMetrics": {"totalDiscussion": "Total Discussion", "sentimentRatio": "Sentiment Ratio", "positiveNegativeRatio": "Positive/Negative Ratio", "mainSource": "Main Source", "sourcePercentage": "percentage", "hotKeyword": "Hot Keyword", "highestDiscussion": "Highest Discussion", "increaseFromPrevious": "from previous period"}, "overview": {"analysisSummary": "Analysis Summary", "mainFindings": "Main Findings", "suggestedAttention": "Suggested Attention", "totalDiscussions": "discussions", "mainPlatform": "Main discussion platform is", "sentimentRatio": "Sentiment ratio is", "positiveMore": "more positive emotions", "negativeMore": "more negative emotions", "hotKeyword": "The hottest related keyword is", "monitorSocialMedia": "Continue monitoring social media platform discussion dynamics", "trackSentimentChanges": "Pay attention to sentiment change trends and respond to negative sentiment in time", "analyzeKeywords": "Analyze hot keywords to understand public attention focus"}, "sentiment": {"sentimentTrend": "Sentiment Trend Analysis", "positiveTrend": "Positive Sentiment Trend", "negativeTrend": "Negative Sentiment Trend", "sentimentTurningPoints": "Sentiment Turning Points", "sentimentDistribution": "Sentiment Distribution Details", "positiveKeywords": "Positive Sentiment Keywords", "neutralKeywords": "Neutral Sentiment Keywords", "negativeKeywords": "Negative Sentiment Keywords", "sentimentSummary": "Sentiment Analysis Summary", "positive": "Positive", "neutral": "Neutral", "negative": "Negative", "articles": "articles", "ofTotalDiscussion": "of total discussion", "mainPositiveKeywords": "Main positive keywords", "mainNeutralKeywords": "Main neutral keywords", "mainNegativeKeywords": "Main negative keywords"}, "topics": {"keywordAnalysis": "Keyword Analysis", "hotTopicsAnalysis": "Hot Topics Analysis", "hotArticles": "Hot Articles", "keywordFrequency": "Keyword Frequency Analysis", "discussionPercentage": "Discussion Percentage", "mentions": "mentions"}, "sources": {"mainMediaSources": "Main Media Sources", "platformActivity": "Platform Activity Analysis", "mediaVolumeRanking": "Main Media Volume Ranking", "ranking": "Ranking", "mediaName": "Media Name", "sentimentTendency": "Sentiment Tendency", "articleCount": "Article Count", "averageInteraction": "Average Interaction per Article", "platformDistribution": "Platform Volume Distribution"}}}