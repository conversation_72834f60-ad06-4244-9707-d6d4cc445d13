{"title": "Reports Center", "subtitle": "Integrate analysis conversations and monitoring topics to generate professional public opinion analysis reports", "createReport": "Create Report", "reportHistory": "Report History", "reportTemplates": "Report Templates", "basicInfo": {"title": "Basic Information", "reportTitle": "Report Title", "reportTitlePlaceholder": "Enter report title", "reportDescription": "Report Description", "reportDescriptionPlaceholder": "Enter report description", "timeRange": "Time Range"}, "chatSelection": {"title": "Select Analysis Conversations", "searchPlaceholder": "Search conversations...", "selectedCount": "selected", "messageCount": "messages", "hasCharts": "with charts", "lastMessage": "Last message"}, "themeSelection": {"title": "Select Analysis Themes", "selectedCount": "selected", "dataCount": "records", "keywords": "keywords", "moreKeywords": "more"}, "options": {"title": "Report Options", "includeCharts": "Include chart analysis", "includeTopPosts": "Include representative posts", "customNotes": "Custom notes", "customNotesPlaceholder": "Enter additional analysis notes or explanations..."}, "actions": {"preview": "Preview Report", "exportFormat": "Export Format", "exportPdf": "Export PDF", "exportPptx": "Export PPTX", "exporting": "Exporting...", "summary": "Report Summary", "chatCount": "Conversations", "themeCount": "Themes", "timeRangeLabel": "Time Range", "includeChartsLabel": "Include Charts", "includePostsLabel": "Include Posts", "yes": "Yes", "no": "No"}, "templates": {"quickTemplates": "Quick Templates", "useTemplate": "Use Template", "brandReport": "Brand Voice Weekly Report", "productAnalysis": "Product Review Analysis", "competitorReport": "Competitor Monitoring Report", "crisisReport": "Crisis Monitoring Report"}, "history": {"title": "Report History", "searchPlaceholder": "Search reports...", "statusFilter": "Status Filter", "allStatuses": "All Statuses", "pagination": {"showing": "Showing", "to": "-", "of": "of", "total": "records", "page": "Page", "totalPages": "of", "pages": "pages"}, "actions": {"view": "View", "download": "Download", "copy": "Copy", "more": "More"}}, "status": {"completed": "Completed", "processing": "Processing", "draft": "Draft", "active": "Active", "paused": "Paused"}, "messages": {"exportSuccess": "Report successfully exported as {format} format! Download link has been sent to your email.", "exportFailed": "Export failed, please try again later.", "noReportsFound": "No reports found matching the criteria", "adjustFilters": "Please try adjusting the search criteria", "noChatsSelected": "No conversations selected", "noThemesSelected": "No themes selected", "selectDataSource": "Please select at least one conversation or theme as data source"}, "reportCard": {"pages": "pages", "charts": "charts", "createdBy": "Created by"}}