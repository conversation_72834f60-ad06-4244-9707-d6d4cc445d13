{"title": "Member Management", "serviceMembers": "Service Members: 10/10", "addMember": "Add Member Account", "user": "User", "role": "Role", "email": "Email", "permissions": "Permission Group", "actions": "Actions", "permissionSettings": "Permission Settings", "confirmDeleteTitle": "Delete User", "confirmDeleteMessage": "Are you sure you want to delete the user '{{username}}'? This action cannot be undone.", "createUser": {"title": "Create New User Account", "editTitle": "Edit User Account", "success": {"title": "Account Created Successfully!", "editTitle": "Account Updated Successfully!", "message": "User account has been successfully created!", "editMessage": "User account has been successfully updated!"}, "fields": {"name": "Username", "namePlaceholder": "Enter username", "email": "Email", "emailPlaceholder": "Enter email address", "password": "Password", "passwordPlaceholder": "Enter password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Re-enter password", "role": "Role"}, "roles": {"admin": "Administrator", "manager": "General User"}, "buttons": {"cancel": "Cancel", "create": "Create Account", "update": "Update Account", "creating": "Creating...", "updating": "Updating..."}, "validation": {"name": {"minLength": "Username must be at least 2 characters", "maxLength": "Username cannot exceed 50 characters", "required": "Name is required"}, "email": {"invalid": "Please enter a valid email address", "taken": "This email address is already in use", "required": "Email is required"}, "password": {"minLength": "Password must be at least 8 characters", "letter": "Password must contain at least one letter", "number": "Password must contain at least one number", "specialChar": "Password must contain at least one special character", "uppercase": "Password must have at least one uppercase character", "lowercase": "Password must have at least one lowercase character"}, "role": "Please select a user role", "username": {"minLength": "Username must be at least 3 characters long", "maxLength": "Username must not exceed 50 characters", "invalidCharacters": "Username can only contain letters, numbers, and underscores"}, "firstName": {"required": "First name is required"}, "lastName": {"required": "Last name is required"}}, "errors": {"roleNotFound": "Role not found"}}, "description": "Manage users and their roles in the system", "addUser": "Add User", "name": "Name", "password": "Password", "selectRole": "Select role", "success": {"userUpdated": "User updated successfully"}, "errors": {"updateFailed": "User updated failed"}, "common": {"createUser": {"success": {"message": "Create user successfully"}}}, "enterName": "Enter name", "enterEmail": "Enter email", "enterPassword": "Enter password", "username": "Username", "enterUsername": "Enter username", "firstName": "Firstname", "enterFirstName": "Enter firstname", "lastName": "Lastname", "enterLastName": "Enter lastname", "updateUser": "Update user"}