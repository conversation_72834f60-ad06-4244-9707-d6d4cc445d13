import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vitest/config";
import path from "path";

export default defineConfig({
  plugins: [react()],
  test: {
    environment: "jsdom",
    globals: true,
    setupFiles: "./src/vitest.setup.ts",
    include: [
      "src/__tests__/**/*.{test,spec}.{js,ts,jsx,tsx}",
      "src/pages/**/__tests__/*.{test,spec}.{js,ts,jsx,tsx}"
    ],
    coverage: {
      provider: "v8",
      reporter: ["text", "html", "json"],
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
