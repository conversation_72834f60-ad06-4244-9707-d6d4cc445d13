{"i18n-ally.localesPaths": ["public/locales"], "i18n-ally.pathMatcher": "{locale}/{namespaces}.json", "i18n-ally.namespace": true, "i18n-ally.keystyle": "nested", "i18n-ally.keyPrefix": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.formatOnType": true, "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always", "typescript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.importModuleSpecifier": "non-relative", "typescript.preferences.importModuleSpecifierEnding": "js", "javascript.preferences.importModuleSpecifierEnding": "js", "path-intellisense.mappings": {"@": "${workspaceRoot}/src"}, "path-intellisense.autoSlashAfterDirectory": true, "path-intellisense.extensionOnImport": true, "path-intellisense.showHiddenFiles": false}