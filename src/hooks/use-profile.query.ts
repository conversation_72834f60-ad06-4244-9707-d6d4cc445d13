import { useAxios } from "@/hooks/use-axios";
import type { UserType } from "@/types/user.type.ts";
import { useQuery } from "@tanstack/react-query";

export const useProfileQuery = () => {
  const { axiosInstance, accessToken } = useAxios();
  return useQuery({
    queryKey: ["profile"],
    queryFn: async () => {
      const response = await axiosInstance.get<UserType>("/profile");
      return response.data;
    },
    enabled: !!accessToken,
    networkMode: "offlineFirst",
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
  });
};
