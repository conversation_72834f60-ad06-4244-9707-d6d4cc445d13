import useAuthStore from "@/stores/useAuthStore";
import type { UserType } from "@/types/user.type";
import { useMutation } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import { useNavigate } from "react-router";
import { useAxios } from "./use-axios";

interface LoginData {
  usernameOrEmail: string;
  password: string;
}

interface LoginResponse {
  accessToken: string;
  user: UserType;
}

export const useLogin = () => {
  const { axiosInstance } = useAxios();
  const setToken = useAuthStore((state) => state.setAccessToken);
  const setUser = useAuthStore((state) => state.setUser);
  const navigate = useNavigate();

  return useMutation<LoginResponse, AxiosError<{ message: string }>, LoginData>(
    {
      mutationFn: async ({ usernameOrEmail, password }) => {
        const { data } = await axiosInstance.post(
          "/auth/login",
          { usernameOrEmail, password },
          { withCredentials: true }
        );
        return data;
      },
      onSuccess: (data) => {
        if (data.accessToken) {
          setToken(data.accessToken);
          setUser(data.user);
          navigate("/");
        }
      },
    }
  );
};
