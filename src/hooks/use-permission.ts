import { pathnamePermissionMap } from "@/constants/permission-pathname.map";
import { useProfileQuery } from "@/hooks/use-profile.query";
import type { Permission } from "@/types/permission.enum";
import PermissionEnum from "@/types/permission.enum";

type SystemPathname = keyof typeof pathnamePermissionMap;

export default function usePermission() {
  const { data: profile, isLoading } = useProfileQuery();

  const hasPermission = (permission: Permission) => {
    if (permission === PermissionEnum.SystemSettings) {
      return (
        profile?.role?.permissions?.includes(PermissionEnum.UserGetList) ||
        profile?.role?.permissions?.includes(PermissionEnum.RoleGetList) ||
        profile?.role?.permissions?.includes(PermissionEnum.AnalysisList) ||
        profile?.role?.permissions?.includes(PermissionEnum.SystemSettings)
      );
    }

    return profile?.role?.permissions?.includes(permission) ?? false;
  };

  const hasPermissionByPathname = (pathname: string) => {
    // First try exact match
    if (pathname in pathnamePermissionMap) {
      return hasPermission(pathnamePermissionMap[pathname as SystemPathname]);
    }

    // Try to match dynamic routes
    const matchedPath = Object.keys(pathnamePermissionMap).find((path) => {
      // Convert path pattern to regex by replacing :param with regex group
      const pathRegex = new RegExp(`^${path.replace(/:[^/]+/g, "([^/]+)")}$`);
      return pathRegex.test(pathname);
    });

    if (matchedPath) {
      return hasPermission(
        pathnamePermissionMap[matchedPath as SystemPathname]
      );
    }
  };

  return {
    hasPermission,
    hasPermissionByPathname,
    isLoading,
  };
}
