import * as React from "react";
import type { ConfirmDialogProps } from "@/components/ui/confirm-dialog";

type ConfirmOptions = Omit<ConfirmDialogProps, 'open' | 'onOpenChange' | 'onConfirm'> & {
  onConfirm: () => void | Promise<void>;
};

export interface ConfirmDialogContextType {
  showConfirm: (options: ConfirmOptions) => void;
}

export const Context = React.createContext<ConfirmDialogContextType | null>(null);

export const useConfirm = () => {
  const context = React.useContext(Context);
  if (!context) {
    throw new Error('useConfirm must be used within a ConfirmDialogProvider');
  }
  return context.showConfirm;
};

// Export as a namespace to allow accessing Context directly
export const useConfirmDialog = {
  Context,
  useConfirm,
};
