import useAuthStore from "@/stores/useAuthStore";
import axios, { AxiosError } from "axios";
import { useMemo } from "react";
import { toast } from "sonner";

const extractErrorMessage = (error: unknown): string => {
  if (error instanceof AxiosError) {
    // Check for response data message
    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    // Check for response status text
    if (error.response?.statusText) {
      return error.response.statusText;
    }

    // Check for request error (no response)
    if (error.request) {
      return "No response received from server";
    }
  }

  // Fallback to error message or default
  return error instanceof Error ? error.message : "An unknown error occurred";
};

const useAxios = () => {
  const accessToken = useAuthStore((state) => state.accessToken);

  const axiosInstance = useMemo(() => {
    const instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
    });

    instance.interceptors.response.use(
      (response) => response,
      (error) => {
        toast.error(extractErrorMessage(error));
        throw error;
      }
    );

    return instance;
  }, [accessToken]);

  return {
    axiosInstance,
    accessToken,
  };
};

export { useAxios };
