import type { ConfirmDialogProps } from "@/components/ui/confirm-dialog";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import * as React from "react";
import { useConfirmDialog } from "./use-confirm-dialog-hook";

// Move the hook to a separate file to satisfy Fast Refresh requirements

type ConfirmOptions = Omit<
  ConfirmDialogProps,
  "open" | "onOpenChange" | "onConfirm"
> & {
  onConfirm: () => void | Promise<void>;
};

interface DialogState {
  isOpen: boolean;
  options: Omit<ConfirmOptions, "onConfirm"> & {
    onConfirm: (() => void | Promise<void>) | null;
  };
}

export function ConfirmDialogProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [dialogState, setDialogState] = React.useState<DialogState>({
    isOpen: false,
    options: {
      title: "",
      description: "",
      onConfirm: null,
    },
  });

  const showConfirm = React.useCallback((options: ConfirmOptions) => {
    const { onConfirm, ...rest } = options;
    setDialogState({
      isOpen: true,
      options: {
        ...rest,
        onConfirm,
      },
    });
  }, []);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setDialogState((prev) => ({
        ...prev,
        isOpen: false,
      }));
    }
  };

  const handleConfirm = async () => {
    if (dialogState.options.onConfirm) {
      await dialogState.options.onConfirm();
      setDialogState((prev) => ({
        ...prev,
        isOpen: false,
      }));
    }
  };

  // Destructure the options we need for the ConfirmDialog
  const { title, description, confirmText, cancelText, variant, isLoading } =
    dialogState.options;

  return (
    <useConfirmDialog.Context.Provider value={{ showConfirm }}>
      {children}
      <ConfirmDialog
        open={dialogState.isOpen}
        onOpenChange={handleOpenChange}
        onConfirm={handleConfirm}
        title={title}
        description={description}
        confirmText={confirmText}
        cancelText={cancelText}
        variant={variant}
        isLoading={isLoading}
      />
    </useConfirmDialog.Context.Provider>
  );
}
