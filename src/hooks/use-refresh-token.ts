import useAuthStore from "@/stores/useAuthStore";
import { useMutation } from "@tanstack/react-query";
import { useAxios } from "./use-axios";

export function useRefreshToken() {
  const setAccessToken = useAuthStore((state) => state.setAccessToken);
  const clearToken = useAuthStore((state) => state.clearToken);
  const { axiosInstance } = useAxios();

  const refreshTokenMutation = useMutation({
    mutationKey: ["refreshToken"],
    mutationFn: async () => {
      const response = await axiosInstance.post(
        "/auth/refresh",
        {},
        { withCredentials: true }
      );
      return response.data.accessToken;
    },
    onSuccess: (accessToken) => {
      setAccessToken(accessToken);
    },
    onError: (error) => {
      console.error("Error refreshing token:", error);
      clearToken();
      window.location.href = "/login";
    },
  });

  return {
    refreshToken: refreshTokenMutation.mutateAsync,
    isRefreshing: refreshTokenMutation.isPending,
    error: refreshTokenMutation.error,
  };
}
