import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function validatePassword(password: string): boolean {
  // Length check
  if (password.length < 8 || password.length > 128) return false;

  // Character checks
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecial = /[@$!%*?&]/.test(password);
  if (!hasLower || !hasUpper || !hasNumber || !hasSpecial) return false;

  // Common weak patterns
  const weakPatterns = [
    "password",
    "123456",
    "qwerty",
    "letmein",
    "welcome",
    "admin",
    "abc123",
    "iloveyou",
    "monkey",
    "dragon",
  ];
  const lowerPassword = password.toLowerCase();
  for (const pattern of weakPatterns) {
    if (lowerPassword.includes(pattern)) return false;
  }

  return true;
}
