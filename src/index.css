@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary-50: #e6f7f7;
  --color-primary-100: #ccf0f0;
  --color-primary-200: #99e0e0;
  --color-primary-300: #66d1d1;
  --color-primary-400: #33c1c1;
  --color-primary-500: #00b2b2;
  --color-primary-600: #008e8e;
  --color-primary-700: #006b6b;
  --color-primary-800: #004747;
  --color-primary-900: #002424;
  --color-primary: #00b2b2;
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary-50: #f0f9f5;
  --color-secondary-100: #e1f4eb;
  --color-secondary-200: #c3e9d7;
  --color-secondary-300: #a5dec3;
  --color-secondary-400: #87d3af;
  --color-secondary-500: #7ed3a9;
  --color-secondary-600: #50c087;
  --color-secondary-700: #3a9e6a;
  --color-secondary-800: #2c784f;
  --color-secondary-900: #1d5235;
  --color-secondary: #7ed3a9;
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-neutral-50: #f5f5f5;
  --color-neutral-100: #ebebeb;
  --color-neutral-200: #d6d6d6;
  --color-neutral-300: #c2c2c2;
  --color-neutral-400: #adadad;
  --color-neutral-500: #999999;
  --color-neutral-600: #777777;
  --color-neutral-700: #5e5e5e;
  --color-neutral-800: #444444;
  --color-neutral-900: #2b2b2b;
  --color-neutral: #777777;

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@utility container {
  margin-inline: auto;
  padding-inline: 2rem;
  @media (width >= --theme(--breakpoint-sm)) {
    max-width: none;
  }
  @media (width >= 1400px) {
    max-width: 1400px;
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 180 100% 35%;
    --primary-foreground: 210 40% 98%;

    --secondary: 151 50% 53%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply bg-background;
  }
  body {
    @apply bg-background text-foreground;
  }
}
