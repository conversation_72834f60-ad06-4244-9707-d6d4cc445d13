import i18n from "i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";
import { initReactI18next } from "react-i18next";

i18n
  .use(Backend) // loads translations via HTTP
  .use(LanguageDetector) // detects user language
  .use(initReactI18next) // binds to React
  .init({
    supportedLngs: ["en-US", "zh-TW"],
    fallbackLng: "en-US", // language to use if detection fails
    debug: false, // set to false in production
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
    react: {
      useSuspense: true, // set true for Suspense support
    },
    detection: {
      order: ["path", "querystring", "cookie", "localStorage", "navigator"],
      caches: ["cookie", "localStorage"],
    },
  });
export default i18n;
