import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useNavigate } from "react-router";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "lucide-react";

export default function ForbiddenPage() {
  const { t } = useTranslation("error");
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-4xl font-bold text-destructive">
            {t("forbidden.status")}
          </CardTitle>
          <h2 className="text-2xl font-semibold">
            {t("forbidden.title")}
          </h2>
        </CardHeader>
        <CardContent className="text-center text-muted-foreground">
          <p className="mb-4">
            {t("forbidden.message")}
          </p>
        </CardContent>
        <CardFooter className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 justify-center">
          <Button 
            variant="outline"
            onClick={() => navigate(-1)}
            className="w-full sm:w-auto"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("forbidden.goBack")}
          </Button>
          <Button 
            onClick={() => navigate('/')}
            className="w-full sm:w-auto"
          >
            {t("forbidden.returnHome")}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
