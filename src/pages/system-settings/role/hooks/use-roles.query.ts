import { useAxios } from "@/hooks/use-axios";
import { useQuery } from "@tanstack/react-query";
import type { RoleData } from "../schemas/role-schemas";

export function useRolesQuery() {
  const { axiosInstance } = useAxios();

  return useQuery<RoleData[]>({
    queryKey: ["roles"],
    queryFn: async () => {
      const response = await axiosInstance.get("/roles");
      return response.data;
    },
  });
}
