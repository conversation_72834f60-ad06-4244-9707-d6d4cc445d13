import { useToast } from "@/components/ui/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import type { RoleData } from "../../../../features/roles/schemas/role-schemas";
import { useAxios } from "../../../../hooks/use-axios";

type CreateRoleData = Omit<RoleData, "id" | "createdAt" | "memberCount">;
type UpdateRoleData = Partial<CreateRoleData> & { id: number };

export function useRoleMutation() {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation("roleManagement");
  const { axiosInstance } = useAxios();

  const createMutation = useMutation({
    mutationFn: async (data: CreateRoleData): Promise<RoleData> => {
      const response = await axiosInstance.post<{
        message: string;
        role: RoleData;
      }>("/roles", data);
      return response.data.role;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast({
        title: t("success.created"),
      });
      navigate("/system-settings/role");
    },
    onError: () => {
      toast({
        title: t("error.createFailed"),
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (data: UpdateRoleData): Promise<RoleData> => {
      const { id, ...updateData } = data;
      const response = await axiosInstance.patch<{
        message: string;
        role: RoleData;
      }>(`/roles/${id}`, updateData);
      return response.data.role;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      queryClient.invalidateQueries({ queryKey: ["role", data.id] });
      toast({
        title: t("success.updated"),
      });
      navigate("/system-settings/role");
    },
    onError: () => {
      toast({
        title: t("error.updateFailed"),
        variant: "destructive",
      });
    },
  });

  return {
    createRole: createMutation.mutateAsync,
    updateRole: updateMutation.mutateAsync,
    isLoading: createMutation.isPending || updateMutation.isPending,
  };
}
