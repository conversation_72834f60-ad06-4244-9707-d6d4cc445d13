import { useAxios } from "@/hooks/use-axios";
import { useQuery } from "@tanstack/react-query";
import type { RoleData } from "../schemas/role-schemas";

export function useRoleQuery(roleId?: string) {
  const { axiosInstance } = useAxios();
  return useQuery({
    queryKey: ["role", roleId],
    queryFn: async () => {
      const response = await axiosInstance.get<RoleData>(`/roles/${roleId}`);
      return response.data;
    },
    enabled: !!roleId,
  });
}
