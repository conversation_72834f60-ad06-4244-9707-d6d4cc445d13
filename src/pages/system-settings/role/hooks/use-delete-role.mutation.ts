import { useAxios } from "@/hooks/use-axios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

export function useDeleteRole() {
  const { t } = useTranslation("roleManagement");
  const queryClient = useQueryClient();
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationFn: async (roleId: number) => {
      const response = await axiosInstance.delete(`/roles/${roleId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast.success(t("success.deleted"));
    },
    onError: () => {
      toast.error(t("errors.deleteFailed"));
    },
  });
}
