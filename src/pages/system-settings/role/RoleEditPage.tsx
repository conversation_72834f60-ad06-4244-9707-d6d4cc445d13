import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router";
import { RoleForm } from "./components/role-form";
import { useRoleQuery } from "./hooks/use-role.query";

export default function RoleEditPage() {
  const { t } = useTranslation("roleManagement");
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { data: role, isLoading } = useRoleQuery(id);

  const handleSuccess = () => {
    navigate("/system-settings/role");
  };

  if (isLoading) {
    return <div>{t("loading")}...</div>;
  }

  if (!role) {
    return <div>{t("roleNotFound")}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">{t("back")}</span>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("editRole")}</h1>
          <p className="text-muted-foreground">{t("editRoleDescription")}</p>
        </div>
      </div>

      <div className="max-w-3xl">
        <RoleForm role={role} onSuccess={handleSuccess} />
      </div>
    </div>
  );
}
