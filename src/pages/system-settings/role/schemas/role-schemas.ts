import { z } from "zod";

type TranslationFunction = (
  key: string,
  options?: Record<string, unknown>
) => string;

// Validation schema for role form
export const createRoleValidationSchema = (t: TranslationFunction) => {
  return z.object({
    name: z
      .string()
      .min(1, t("validation.nameRequired"))
      .min(3, t("validation.nameMinLength"))
      .max(50, t("validation.nameMaxLength")),
    description: z
      .string()
      .max(255, t("validation.descriptionMaxLength"))
      .optional(),
    permissions: z
      .array(z.string())
      .min(1, t("validation.permissionsRequired")),
  });
};

export interface RoleData {
  id?: number;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
}
