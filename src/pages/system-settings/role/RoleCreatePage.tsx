import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
import { RoleForm } from "./components/role-form";

export default function RoleCreatePage() {
  const { t } = useTranslation("roleManagement");
  const navigate = useNavigate();

  const handleSuccess = () => {
    navigate("/system-settings/role");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          size="icon"
          className="h-8 w-8"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">{t("back")}</span>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            {t("createRole")}
          </h1>
          <p className="text-muted-foreground">{t("createRoleDescription")}</p>
        </div>
      </div>

      <div className="max-w-3xl">
        <RoleForm onSuccess={handleSuccess} />
      </div>
    </div>
  );
}
