import { useRolesQuery } from "@/pages/system-settings/role/hooks/use-roles.query";
import { Command } from "lucide-react";
import { useTranslation } from "react-i18next";
import { GradientHeader } from "../../../components/gradient-header";
import { RolesTable } from "./components/roles-table";

export default function SystemSettingsRolePage() {
  const { t } = useTranslation("roleManagement");
  const { data: roles = [], isLoading } = useRolesQuery();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <GradientHeader
        icon={Command}
        title={t("title")}
        description={t("description")}
      />

      <RolesTable roles={roles} />
    </div>
  );
}
