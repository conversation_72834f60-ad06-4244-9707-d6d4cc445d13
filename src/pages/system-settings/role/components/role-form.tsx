import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { usePermissionsQuery } from "@/features/permissions/hooks/use-permissions.query";
import { useRoleMutation } from "@/pages/system-settings/role/hooks/use-role.mutation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";
import type { RoleData } from "../schemas/role-schemas";
import { createRoleValidationSchema } from "../schemas/role-schemas";
import { PermissionsCombobox } from "./permissions-combobox";
import { PermissionsTable } from "./permissions-table";

interface RoleFormProps {
  role?: RoleData;
  onSuccess?: () => void;
}

export function RoleForm({ role, onSuccess }: RoleFormProps) {
  const { t } = useTranslation("roleManagement");
  const isEditMode = !!role;
  const { createRole, updateRole, isLoading } = useRoleMutation();

  const { data: availablePermissions = [], isLoading: isLoadingPermissions } =
    usePermissionsQuery();

  const form = useForm<RoleData>({
    resolver: zodResolver(createRoleValidationSchema(t)),
    defaultValues: role || {
      name: "",
      description: "",
      permissions: [],
    },
  });

  const onSubmit = async (formData: RoleData) => {
    try {
      if (isEditMode && role?.id) {
        await updateRole({ id: role.id, ...formData });
      } else {
        await createRole({
          name: formData.name,
          description: formData.description,
          permissions: formData.permissions,
        });
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error("Error saving role:", error);
    }
  };

  return (
    <Card>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="mt-3">
                  <FormLabel>
                    {t("roleName")} <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("description")}</FormLabel>
                  <FormControl>
                    <Textarea {...field} className="min-h-[100px]" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">{t("permissions")}</h3>
              <div className="space-x-2">
                <div className="flex-1">
                  <Label htmlFor="add-permission">{t("addPermission")}</Label>
                  <div className="flex w-full space-x-2">
                    <PermissionsCombobox
                      availablePermissions={availablePermissions}
                      currentPermissions={form.watch("permissions")}
                      onSelect={(value) => {
                        const currentPermissions =
                          form.getValues("permissions");
                        if (!currentPermissions.includes(value)) {
                          form.setValue(
                            "permissions",
                            [...currentPermissions, value],
                            { shouldValidate: true }
                          );
                        }
                      }}
                      placeholder={t("selectPermission")}
                      emptyText={t("noPermissionsAvailable")}
                    />
                  </div>
                </div>

                <PermissionsTable
                  permissions={form.watch("permissions")}
                  isLoading={isLoadingPermissions}
                  onRemovePermission={(permission) => {
                    const updatedPermissions = form
                      .getValues("permissions")
                      .filter((p) => p !== permission);
                    form.setValue("permissions", updatedPermissions, {
                      shouldValidate: true,
                    });
                  }}
                  noPermissionsText={t("noPermissions")}
                  loadingText={t("loadingPermissions")}
                />
                <FormMessage>
                  {form.formState.errors.permissions?.message}
                </FormMessage>
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <Link to="/system-settings/role">
                <Button type="button" variant="outline">
                  {t("cancel")}
                </Button>
              </Link>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <span className="mr-2">{t("saving")}...</span>
                  </>
                ) : isEditMode ? (
                  t("updateRole")
                ) : (
                  t("createRole")
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
