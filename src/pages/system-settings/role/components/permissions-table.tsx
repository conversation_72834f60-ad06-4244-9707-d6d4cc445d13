import { useMemo } from "react";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface PermissionsTableProps {
  permissions: string[];
  isLoading: boolean;
  onRemovePermission: (permission: string) => void;
  noPermissionsText: string;
  loadingText: string;
}

export function PermissionsTable({
  permissions,
  isLoading,
  onRemovePermission,
  noPermissionsText,
  loadingText,
}: PermissionsTableProps) {
  const tableRows = useMemo(() => {
    if (permissions.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={2} className="h-24 text-center">
            {noPermissionsText}
          </TableCell>
        </TableRow>
      );
    }
    
    return permissions.map((permission) => (
      <TableRow key={permission}>
        <TableCell className="font-medium">{permission}</TableCell>
        <TableCell className="text-right">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onRemovePermission(permission)}
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </TableCell>
      </TableRow>
    ));
  }, [permissions, noPermissionsText, onRemovePermission]);

  if (isLoading) {
    return (
      <Table className="mt-3">
        <TableBody>
          <TableRow>
            <TableCell colSpan={2} className="h-24 text-center">
              {loadingText}
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );
  }

  return (
    <Table className="mt-3">
      <TableHeader>
        <TableRow>
          <TableHead>Permission Name</TableHead>
          <TableHead className="w-[100px] text-right">Action</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>{tableRows}</TableBody>
    </Table>
  );
}
