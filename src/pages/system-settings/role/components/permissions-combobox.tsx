import { useMemo } from "react";
import { Combobox } from "@/components/ui/combobox";

interface PermissionsComboboxProps {
  availablePermissions: string[];
  currentPermissions: string[];
  onSelect: (value: string) => void;
  placeholder: string;
  emptyText: string;
}

export function PermissionsCombobox({
  availablePermissions,
  currentPermissions,
  onSelect,
  placeholder,
  emptyText,
}: PermissionsComboboxProps) {
  const filteredOptions = useMemo(
    () =>
      availablePermissions
        .filter((p) => !currentPermissions.includes(p))
        .map((permission) => ({
          value: permission,
          label: permission,
        })),
    [availablePermissions, currentPermissions]
  );

  return (
    <Combobox
      options={filteredOptions}
      onValueChange={(value) => value && onSelect(value)}
      placeholder={placeholder}
      emptyText={emptyText}
      buttonClassName="w-full"
    />
  );
}
