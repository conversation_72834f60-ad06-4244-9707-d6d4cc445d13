import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useConfirm } from "@/hooks/use-confirm-dialog-hook";
import usePermission from "@/hooks/use-permission.ts";
import { useDeleteRole } from "@/pages/system-settings/role/hooks/use-delete-role.mutation";
import PermissionEnum from "@/types/permission.enum";
import { Edit, Plus, Trash2 } from "lucide-react";
import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";
import type { RoleData } from "../schemas/role-schemas";

interface RolesTableProps {
  roles: RoleData[];
}

export function RolesTable({ roles }: RolesTableProps) {
  const { t } = useTranslation("roleManagement");
  const confirm = useConfirm();
  const { mutate: deleteRole } = useDeleteRole();
  const { hasPermission } = usePermission();

  const handleDelete = useCallback(
    (role: RoleData) => {
      if (!role.id) return;

      confirm({
        title: t("confirmDeleteTitle"),
        description: t("confirmDeleteMessage", { name: role.name }),
        variant: "destructive",
        onConfirm: () => {
          deleteRole(role.id!);
        },
      });
    },
    [confirm, deleteRole, t]
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-end">
        {hasPermission(PermissionEnum.RoleCreate) && (
          <Link to="/system-settings/role/new">
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              {t("addRole")}
            </Button>
          </Link>
        )}
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t("name")}</TableHead>
                <TableHead>{t("description")}</TableHead>
                <TableHead>{t("permissions")}</TableHead>
                <TableHead>{t("createdAt")}</TableHead>
                <TableHead className="w-[100px]">{t("actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">{role.name}</TableCell>
                  <TableCell className="text-muted-foreground">
                    {role.description || "-"}
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {role.permissions.slice(0, 2).map((permission) => (
                        <Badge key={permission} variant="secondary">
                          {permission}
                        </Badge>
                      ))}
                      {role.permissions.length > 2 && (
                        <Badge variant="outline">
                          +{role.permissions.length - 2} {t("more")}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {new Date(role.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {hasPermission(PermissionEnum.RoleEdit) && (
                        <Link to={`/system-settings/role/${role.id}/edit`}>
                          <Button variant="ghost" size="icon">
                            <Edit className="w-4 h-4" />
                          </Button>
                        </Link>
                      )}

                      {hasPermission(PermissionEnum.RoleDelete) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(role)}
                        >
                          <Trash2 className="w-4 h-4 text-destructive" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
