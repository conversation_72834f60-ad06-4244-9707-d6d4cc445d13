/* eslint-disable @typescript-eslint/no-explicit-any */
import { render, screen, fireEvent } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router";
import { beforeEach, describe, expect, it, vi } from "vitest";
import RoleEditPage from "../RoleEditPage";

// Mock the RoleForm component
vi.mock("../components/role-form", () => ({
  RoleForm: ({ role, onSuccess }: { role: any; onSuccess: () => void }) => (
    <div data-testid="role-form">
      <div data-testid="role-name">{role?.name}</div>
      <button onClick={onSuccess}>Mock Submit</button>
    </div>
  ),
}));

// Mock the useRoleQuery hook
const mockUseRoleQuery = vi.fn();
vi.mock("../hooks/use-role.query", () => ({
  useRoleQuery: (id: string) => mockUseRoleQuery(id),
}));

// Mock react-router
const mockNavigate = vi.fn();
vi.mock("react-router", () => ({
  ...vi.importActual("react-router"),
  useNavigate: () => mockNavigate,
  useParams: () => ({
    id: "1",
  }),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "roleManagement:editRole": "Edit Role",
        "roleManagement:editRoleDescription":
          "Edit role details and permissions",
        "roleManagement:back": "Back",
        "roleManagement:loading": "Loading",
        "roleManagement:roleNotFound": "Role not found",
      };
      return translations[key] || key;
    },
  }),
}));

describe("RoleEditPage", () => {
  const mockRole = {
    id: "1",
    name: "Test Role",
    description: "Test Description",
    permissions: ["permission:create", "permission:edit"],
    createdAt: "2023-01-01T00:00:00.000Z",
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock for useRoleQuery
    mockUseRoleQuery.mockReturnValue({
      data: mockRole,
      isLoading: false,
      isError: false,
    });
  });

  const renderComponent = () => {
    return render(
      <MemoryRouter initialEntries={["/system-settings/role/1/edit"]}>
        <Routes>
          <Route
            path="/system-settings/role/:id/edit"
            element={<RoleEditPage />}
          />
        </Routes>
      </MemoryRouter>
    );
  };

  it("renders the edit role page with title and description when data is loaded", () => {
    renderComponent();

    expect(screen.getByText("Edit Role")).toBeInTheDocument();
    expect(
      screen.getByText("Edit role details and permissions")
    ).toBeInTheDocument();
    expect(screen.getByTestId("role-form")).toBeInTheDocument();
    expect(screen.getByTestId("role-name").textContent).toBe(mockRole.name);
  });

  it("shows loading state while fetching role data", () => {
    mockUseRoleQuery.mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });

    renderComponent();

    expect(screen.getByText("Loading...")).toBeInTheDocument();
    expect(screen.queryByTestId("role-form")).not.toBeInTheDocument();
  });

  it("shows error message when role is not found", () => {
    mockUseRoleQuery.mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });

    renderComponent();

    expect(screen.getByText("Role not found")).toBeInTheDocument();
    expect(screen.queryByTestId("role-form")).not.toBeInTheDocument();
  });

  it("navigates back when back button is clicked", () => {
    renderComponent();

    const backButton = screen.getByLabelText("Back");
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it("navigates to roles list on successful role update", () => {
    renderComponent();

    // Simulate form submission success
    const submitButton = screen.getByText("Mock Submit");
    fireEvent.click(submitButton);

    // Should navigate to the roles list
    expect(mockNavigate).toHaveBeenCalledWith("/system-settings/role");
  });

  it("passes the correct role data to the RoleForm component", () => {
    renderComponent();

    // Check if RoleForm received the correct role data
    expect(screen.getByTestId("role-name").textContent).toBe(mockRole.name);
  });
});
