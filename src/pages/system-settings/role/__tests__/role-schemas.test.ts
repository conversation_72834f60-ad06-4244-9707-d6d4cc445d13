import { describe, expect, it } from "vitest";
import {
  createRoleValidationSchema,
  type RoleData,
} from "../schemas/role-schemas";

describe("Role Schemas", () => {
  // Mock translation function
  const t = (key: string, options?: Record<string, unknown>): string => {
    const translations: Record<string, string> = {
      "validation.nameRequired": "Name is required",
      "validation.nameMinLength": "Name must be at least 3 characters",
      "validation.nameMaxLength": "Name must be at most 50 characters",
      "validation.descriptionMaxLength":
        "Description must be at most 255 characters",
      "validation.permissionsRequired": "At least one permission is required",
    };

    if (options) {
      return `${translations[key]} ${JSON.stringify(options)}`;
    }
    return translations[key] || key;
  };

  describe("createRoleValidationSchema", () => {
    const schema = createRoleValidationSchema(t);

    it("should validate a valid role object", () => {
      const validRole = {
        name: "Test Role",
        description: "A test role",
        permissions: ["permission:create"],
      };

      const result = schema.safeParse(validRole);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validRole);
      }
    });

    it("should require name", () => {
      const invalidRole = {
        name: "",
        description: "A test role",
        permissions: ["permission:create"],
      };

      const result = schema.safeParse(invalidRole);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe("Name is required");
      }
    });

    it("should enforce minimum length for name", () => {
      const invalidRole = {
        name: "ab", // Less than 3 characters
        description: "A test role",
        permissions: ["permission:create"],
      };

      const result = schema.safeParse(invalidRole);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "Name must be at least 3 characters"
        );
      }
    });

    it("should enforce maximum length for name", () => {
      const invalidRole = {
        name: "a".repeat(51), // More than 50 characters
        description: "A test role",
        permissions: ["permission:create"],
      };

      const result = schema.safeParse(invalidRole);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "Name must be at most 50 characters"
        );
      }
    });

    it("should enforce maximum length for description", () => {
      const invalidRole = {
        name: "Test Role",
        description: "a".repeat(256), // More than 255 characters
        permissions: ["permission:create"],
      };

      const result = schema.safeParse(invalidRole);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "Description must be at most 255 characters"
        );
      }
    });

    it("should make description optional", () => {
      const validRole = {
        name: "Test Role",
        permissions: ["permission:create"],
      };

      const result = schema.safeParse(validRole);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.description).toBeUndefined();
      }
    });

    it("should require at least one permission", () => {
      const invalidRole = {
        name: "Test Role",
        description: "A test role",
        permissions: [],
      };

      const result = schema.safeParse(invalidRole);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe(
          "At least one permission is required"
        );
      }
    });
  });

  describe("RoleData interface", () => {
    it("should match the expected shape", () => {
      const roleData: RoleData = {
        id: 1,
        name: "Test Role",
        description: "A test role",
        permissions: ["permission:create"],

        createdAt: "2023-01-01T00:00:00.000Z",
      };

      expect(roleData).toHaveProperty("id");
      expect(roleData).toHaveProperty("name");
      expect(roleData).toHaveProperty("description");
      expect(roleData).toHaveProperty("permissions");
      expect(roleData).toHaveProperty("createdAt");

      // Check types
      expect(typeof roleData.name).toBe("string");
      expect(typeof roleData.description).toBe("string");
      expect(Array.isArray(roleData.permissions)).toBe(true);
      expect(typeof roleData.createdAt).toBe("string");
    });
  });
});
