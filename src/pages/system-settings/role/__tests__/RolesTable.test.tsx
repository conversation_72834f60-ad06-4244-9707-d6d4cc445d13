import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { RolesTable } from "../components/roles-table";
import type { RoleData } from "../schemas/role-schemas";

// Mock implementations with proper typing
const mockUseConfirm = vi.fn().mockReturnValue({});
const mockUseDeleteRole = vi.fn().mockReturnValue({});
const mockHasPermission = vi.fn().mockImplementation(() => true);
const mockUsePermission = vi.fn().mockImplementation(() => ({
  hasPermission: mockHasPermission
}));

// Mock the hooks and components
vi.mock("@/hooks/use-confirm-dialog-hook", () => ({
  useConfirm: () => mockUseConfirm(),
}));

vi.mock("@/hooks/use-permission", () => ({
  default: () => mockUsePermission(),
}));

vi.mock("../hooks/use-delete-role.mutation", () => ({
  useDeleteRole: () => mockUseDeleteRole(),
}));

// Mock react-router-dom
vi.mock("react-router-dom", () => ({
  Link: ({ to, children }: { to: string; children: React.ReactNode }) => (
    <a href={to}>{children}</a>
  ),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "roleManagement:name": "Name",
        "roleManagement:description": "Description",
        "roleManagement:permissions": "Permissions",
        "roleManagement:createdAt": "Created At",
        "roleManagement:actions": "Actions",
        "roleManagement:addRole": "Add Role",
        "roleManagement:confirmDeleteTitle": "Delete Role",
        "roleManagement:confirmDeleteMessage": `Are you sure you want to delete {name}?`,
        "roleManagement:more": "more",
      };
      return translations[key] || key;
    },
  }),
}));

describe("RolesTable", () => {
  const mockRoles: RoleData[] = [
    {
      id: 1,
      name: "Admin",
      description: "Administrator with full access",
      permissions: ["user:create", "user:edit", "user:delete"],
      createdAt: "2023-01-01T00:00:00.000Z",
    },
    {
      id: 2,
      name: "Editor",
      description: "Can edit content",
      permissions: ["content:create", "content:edit"],
      createdAt: "2023-01-02T00:00:00.000Z",
    },
  ];

  const mockMutate = vi.fn().mockResolvedValue(undefined);
  const mockConfirm = vi.fn().mockImplementation(({ onConfirm }) => {
    onConfirm();
    return Promise.resolve(true);
  });

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mocks
    mockUseConfirm.mockReturnValue({
      confirm: mockConfirm,
    });

    mockUseDeleteRole.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
    });

    mockUsePermission.mockReturnValue({
      hasPermission: (permission: string) => {
        const permissions = ["role:create", "role:edit", "role:delete"];
        return permissions.includes(permission);
      },
    });
  });

  it("renders the table with role data", () => {
    render(<RolesTable roles={mockRoles} />);

    // Check table headers
    expect(screen.getByText("Name")).toBeInTheDocument();
    expect(screen.getByText("Description")).toBeInTheDocument();
    expect(screen.getByText("Permissions")).toBeInTheDocument();
    expect(screen.getByText("Created At")).toBeInTheDocument();
    expect(screen.getByText("Actions")).toBeInTheDocument();

    // Check role data is displayed
    mockRoles.forEach((role) => {
      expect(screen.getByText(role.name)).toBeInTheDocument();
      expect(screen.getByText(role.description)).toBeInTheDocument();

      // Check first two permissions are shown
      role.permissions.slice(0, 2).forEach((permission) => {
        expect(screen.getByText(permission)).toBeInTheDocument();
      });

      // Check "more" badge is shown if there are more than 2 permissions
      if (role.permissions.length > 2) {
        expect(
          screen.getByText(`+${role.permissions.length - 2} more`)
        ).toBeInTheDocument();
      }

      // Check formatted date
      const formattedDate = new Date(role.createdAt).toLocaleDateString();
      expect(screen.getByText(formattedDate)).toBeInTheDocument();
    });
  });

  it("shows the add role button when user has create permission", () => {
    render(<RolesTable roles={mockRoles} />);

    const addButton = screen.getByRole("link", { name: /add role/i });
    expect(addButton).toBeInTheDocument();
    expect(addButton).toHaveAttribute("href", "/system-settings/role/new");
  });

  it("hides the add role button when user doesn't have create permission", () => {
    mockUsePermission.mockReturnValue({
      hasPermission: () => false,
    });

    render(<RolesTable roles={mockRoles} />);

    expect(
      screen.queryByRole("link", { name: /add role/i })
    ).not.toBeInTheDocument();
  });

  it("shows confirmation dialog when delete button is clicked", () => {
    render(<RolesTable roles={mockRoles} />);

    // Mock the confirm dialog is already set up in beforeEach

    // Find and click the delete button for the first role
    const deleteButtons = screen.getAllByRole("button", { name: /delete/i });
    fireEvent.click(deleteButtons[0]);

    expect(mockConfirm).toHaveBeenCalledWith(
      expect.objectContaining({
        title: "Delete Role",
        description: `Are you sure you want to delete ${mockRoles[0].name}?`,
      })
    );

    // Verify the delete mutation was called
    expect(mockMutate).toHaveBeenCalledWith(mockRoles[0].id);
  });

  it("shows edit link when user has edit permission", () => {
    render(<RolesTable roles={mockRoles} />);

    // Should be one edit button per role
    const editLinks = screen.getAllByRole("link", { name: /edit/i });
    expect(editLinks).toHaveLength(mockRoles.length);

    // Check the first edit link points to the correct URL
    expect(editLinks[0]).toHaveAttribute(
      "href",
      `/system-settings/role/${mockRoles[0].id}/edit`
    );
  });

  it("hides action buttons when user doesn't have permissions", () => {
    // User has no permissions
    mockUsePermission.mockReturnValue({
      hasPermission: () => false,
    });

    render(<RolesTable roles={mockRoles} />);

    // Check all action buttons are hidden
    expect(
      screen.queryByRole("link", { name: /add role/i })
    ).not.toBeInTheDocument();
    expect(screen.queryAllByRole("link", { name: /edit/i })).toHaveLength(0);
    expect(screen.queryAllByRole("button", { name: /delete/i })).toHaveLength(
      0
    );
  });
});
