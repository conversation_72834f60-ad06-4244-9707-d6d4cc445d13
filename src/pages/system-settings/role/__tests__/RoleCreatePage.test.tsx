import { fireEvent, render, screen } from "@testing-library/react";
import { MemoryRouter } from "react-router";
import { beforeEach, describe, expect, it, vi } from "vitest";
import RoleCreatePage from "../RoleCreatePage";

// Mock components and hooks
vi.mock("../components/role-form", () => ({
  RoleForm: ({ onSuccess }: { onSuccess: () => void }) => (
    <div data-testid="role-form">
      <button onClick={onSuccess} data-testid="submit-button">
        Mock Submit
      </button>
    </div>
  ),
}));

const mockNavigate = vi.fn();
vi.mock("react-router", async () => {
  const actual = await vi.importActual("react-router");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

vi.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        "createRole": "Create Role",
        "createRoleDescription": "Create a new role with specific permissions",
        "back": "Back",
      };
      return translations[key] || key;
    },
  }),
}));

describe("RoleCreatePage", () => {
  const renderPage = () => {
    return render(
      <MemoryRouter>
        <RoleCreatePage />
      </MemoryRouter>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders page title and description", () => {
    renderPage();
    expect(screen.getByText("Create Role")).toBeInTheDocument();
    expect(screen.getByText("Create a new role with specific permissions")).toBeInTheDocument();
  });

  it("renders back button with correct navigation", () => {
    renderPage();
    // Find the back button by its aria-hidden SVG icon
    const backButton = screen.getByRole("button", { name: /back/i });
    fireEvent.click(backButton);
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it("renders RoleForm with success handler", () => {
    renderPage();
    const form = screen.getByTestId("role-form");
    expect(form).toBeInTheDocument();
    
    fireEvent.click(screen.getByTestId("submit-button"));
    expect(mockNavigate).toHaveBeenCalledWith("/system-settings/role");
  });

  it("has correct layout structure", () => {
    renderPage();
    expect(screen.getByTestId("role-form").closest(".max-w-3xl")).toBeInTheDocument();
  });
});
