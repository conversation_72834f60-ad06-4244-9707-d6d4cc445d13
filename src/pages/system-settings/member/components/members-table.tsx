import { <PERSON><PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { useConfirm } from "@/hooks/use-confirm-dialog-hook";
import usePermission from "@/hooks/use-permission.ts";
import PermissionEnum from "@/types/permission.enum";
import type { UserType } from "@/types/user.type.ts";
import { Edit, Trash2 } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDeleteUserMutation } from "../hooks/use-delete-user.mutation";
import { UserFormModal } from "./user-form-modal";

interface MembersTableProps {
  users: UserType[];
}

export function MembersTable({ users }: MembersTableProps) {
  const { t: common } = useTranslation("common");
  const { t } = useTranslation("memberManagement");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<UserType | null>(null);
  const confirm = useConfirm();
  const deleteUserMutation = useDeleteUserMutation();
  const { hasPermission } = usePermission();

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-end">
        {hasPermission(PermissionEnum.UserCreate) && (
          <UserFormModal
            open={isCreateModalOpen}
            onOpenChange={setIsCreateModalOpen}
          />
        )}

        {editingUser && (
          <UserFormModal
            user={editingUser}
            open={editingUser !== null}
            onOpenChange={() => {
              setEditingUser(null);
            }}
            onSuccess={() => {
              setEditingUser(null);
            }}
          />
        )}
      </CardHeader>

      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">{t("user")}</TableHead>
                <TableHead>{t("email")}</TableHead>
                <TableHead>{t("role")}</TableHead>
                <TableHead className="text-right">{t("actions")}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {user.username.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span>{user.username}</span>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.role?.name || "-"}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      {hasPermission(PermissionEnum.UserEdit) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          aria-label="Edit user"
                          onClick={() => {
                            setEditingUser(user);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}

                      {hasPermission(PermissionEnum.UserDelete) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-600 hover:text-red-700"
                          aria-label={`Delete user ${user.username}`}
                          onClick={() => {
                            confirm({
                              title: t("confirmDeleteTitle"),
                              description: t("confirmDeleteMessage", {
                                username: user.username,
                              }),
                              variant: "destructive",
                              confirmText: common("delete"),
                              onConfirm: () =>
                                deleteUserMutation.mutate(user.id),
                            });
                          }}
                          disabled={deleteUserMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
