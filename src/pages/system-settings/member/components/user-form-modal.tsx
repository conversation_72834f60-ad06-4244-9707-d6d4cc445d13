import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCreateUserQuery } from "@/pages/system-settings/member/hooks/use-create-user.query";
import { useUpdateUserMutation } from "@/pages/system-settings/member/hooks/use-update-user.mutation";
import type { UserType } from "@/types/user.type.ts";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";
import { useRolesQuery } from "../hooks/use-roles.query";

interface UserFormModalProps {
  user?: UserType; // Existing user data when in edit mode
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
}

export function UserFormModal({
  user,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  onSuccess,
}: UserFormModalProps) {
  const [isOpen, setIsOpen] = useState(externalOpen || false);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (externalOnOpenChange) {
      externalOnOpenChange(open);
    }
  };
  const { t: common } = useTranslation("common");
  const { t } = useTranslation("memberManagement");
  const { mutate: createUser, isPending } = useCreateUserQuery();
  const { data: roles = [], isLoading: isLoadingRoles } = useRolesQuery();
  const { mutate: updateMember } = useUpdateUserMutation();

  const createUserSchema = () => {
    return z.object({
      username: z
        .string()
        .min(3, t("createUser.validation.username.minLength"))
        .max(50, t("createUser.validation.username.maxLength"))
        .regex(
          /^[a-zA-Z0-9_]+$/,
          t("createUser.validation.username.invalidCharacters")
        )
        .trim(),
      firstName: z
        .string()
        .min(1, t("createUser.validation.firstName.required"))
        .trim(),
      lastName: z
        .string()
        .min(1, t("createUser.validation.lastName.required"))
        .trim(),
      email: z
        .string()
        .email(t("createUser.validation.email.invalid"))
        .min(1, t("createUser.validation.email.required"))
        .trim(),
      password: z
        .string()
        .min(8, t("createUser.validation.password.minLength"))
        .regex(/[A-Z]/, {
          message: t("createUser.validation.password.uppercase"),
        })
        .regex(/[a-z]/, {
          message: t("createUser.validation.password.lowercase"),
        })
        .regex(/[0-9]/, {
          message: t("createUser.validation.password.number"),
        })
        .regex(/[^a-zA-Z0-9]/, {
          message: t("createUser.validation.password.specialChar"),
        })
        .trim(),
      role: z.string().min(1, t("createUser.validation.role")),
    });
  };

  const updateUserSchema = () => {
    return z.object({
      username: z
        .string()
        .min(3, t("createUser.validation.username.minLength"))
        .max(50, t("createUser.validation.username.maxLength"))
        .regex(
          /^[a-zA-Z0-9_]+$/,
          t("createUser.validation.username.invalidCharacters")
        )
        .trim(),
      firstName: z
        .string()
        .min(1, t("createUser.validation.firstName.required"))
        .trim(),
      lastName: z
        .string()
        .min(1, t("createUser.validation.lastName.required"))
        .trim(),
      email: z
        .string()
        .email(t("createUser.validation.email.invalid"))
        .min(1, t("createUser.validation.email.required"))
        .trim(),
      password: z
        .union([
          z.string().length(0), // Allow empty string
          z
            .string()
            .min(8, t("createUser.validation.password.minLength"))
            .regex(/[A-Z]/, {
              message: t("createUser.validation.password.uppercase"),
            })
            .regex(/[a-z]/, {
              message: t("createUser.validation.password.lowercase"),
            })
            .regex(/[0-9]/, {
              message: t("createUser.validation.password.number"),
            })
            .regex(/[^a-zA-Z0-9]/, {
              message: t("createUser.validation.password.specialChar"),
            })
            .trim(),
        ])
        .optional(),
      role: z.string().min(1, t("createUser.validation.role")),
    });
  };

  type CreateFormValues = z.infer<ReturnType<typeof createUserSchema>>;
  type UpdateFormValues = Omit<CreateFormValues, "password"> & {
    password?: string;
  };

  type FormValues = UserType extends undefined
    ? CreateFormValues
    : UpdateFormValues;

  const form = useForm<FormValues>({
    resolver: user
      ? zodResolver(updateUserSchema())
      : zodResolver(createUserSchema()),
    defaultValues: user
      ? {
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          password: "", // Empty password by default for updates
          role: user.role.name,
        }
      : {
          username: "",
          firstName: "",
          lastName: "",
          email: "",
          password: "",
          role: "",
        },
  });

  useEffect(() => {
    if (externalOpen !== undefined) {
      setIsOpen(externalOpen);
    }
  }, [externalOpen]);

  const onSubmit = (values: FormValues) => {
    const selectedRole = roles?.find((role) => role.name === values.role);
    if (!selectedRole) {
      console.error("Selected role not found");
      return;
    }
    const roleId = selectedRole.id;
    if (user) {
      const updateData = {
        id: user.id,
        username: values.username,
        email: values.email,
        firstName: values.firstName,
        lastName: values.lastName,
        roleId,
        ...(values.password && { password: values.password }), // Only include password if provided
      };

      updateMember(updateData, {
        onSuccess: () => {
          form.reset();
          setIsOpen(false);
        },
      });
      if (onSuccess) {
        onSuccess();
        form.reset();
        setIsOpen(false);
      }
    } else {
      createUser(
        {
          username: values.username,
          email: values.email,
          firstName: values.firstName,
          lastName: values.lastName,
          password: values.password as string,
          roleId,
        },
        {
          onSuccess: () => {
            form.reset();
            setIsOpen(false);
          },
        }
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      {!user && (
        <DialogTrigger asChild>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t("addUser")}
          </Button>
        </DialogTrigger>
      )}

      <DialogContent
        className="sm:max-w-[425px]"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            {user ? t("updateUser") : t("createUser.title")}
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("username")}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t("enterUsername")}
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("firstName")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("enterFirstName")}
                        disabled={isPending}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("lastName")}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t("enterLastName")}
                        disabled={isPending}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("email")}</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={t("enterEmail")}
                      disabled={isPending}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("password")}</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder={t("enterPassword")}
                        disabled={isPending}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            }

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("role")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isPending}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t("selectRole")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoadingRoles ? (
                        <div className="p-2 text-center">
                          <span>{common("loading")}</span>
                        </div>
                      ) : (
                        roles.map((role) => (
                          <SelectItem key={role.id} value={role.name}>
                            <span>{role.name}</span>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isPending}
              >
                {common("cancel")}
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {user ? common("updating") : common("creating")}
                  </>
                ) : user ? (
                  t("updateUser")
                ) : (
                  common("create")
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
