import { Grad<PERSON>Header } from "@/components/gradient-header";
import { useUsersQuery } from "@/pages/system-settings/member/hooks/use-users.query";
import { Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { MembersTable } from "./components/members-table";

export default function SystemSettingsMemberPage() {
  const { t } = useTranslation("memberManagement");
  const { data: users = [] } = useUsersQuery();

  return (
    <div className="space-y-6">
      <GradientHeader
        icon={Users}
        title={t("title")}
        description={t("description")}
      />
      <MembersTable users={users} />
    </div>
  );
}
