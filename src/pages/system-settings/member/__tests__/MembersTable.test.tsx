/* eslint-disable @typescript-eslint/no-explicit-any */
import type { UserType } from "@/types/user.type.ts";
import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { MembersTable } from "../components/members-table";

// Mock dependencies
const mockUsePermission = vi.fn();
const mockUseDeleteUserMutation = vi.fn();
const mockUserFormModal = vi.fn();
const mockConfirm = vi.fn();

// Mock the hooks and components
vi.mock("@/hooks/use-confirm-dialog-hook", () => ({
  useConfirm: () => mockConfirm,
}));

vi.mock("@/hooks/use-permission.ts", () => ({
  default: () => mockUsePermission(),
}));

vi.mock("../hooks/use-delete-user.mutation", () => ({
  useDeleteUserMutation: () => mockUseDeleteUserMutation(),
}));

vi.mock("../components/user-form-modal", () => ({
  UserFormModal: (props: any) => mockUserFormModal(props),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => {
    return {
      t: (key: string) => {
        const translations: { [key: string]: string } = {
          // Common translations
          "common:delete": "Delete",

          // Member Management translations
          "memberManagement:user": "User",
          "memberManagement:email": "Email",
          "memberManagement:role": "Role",
          "memberManagement:actions": "Actions",
          "memberManagement:confirmDeleteTitle": "Delete User",
          "memberManagement:confirmDeleteMessage": `Are you sure you want to delete {username}?`,
        };
        return translations[key] || key;
      },
    };
  },
}));

describe("MembersTable", () => {
  const mockUsers: UserType[] = [
    {
      id: 1,
      username: "testuser1",
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User1",
      role: {
        id: 1,
        name: "Admin",
        description: "Administrator role with full access",
        permissions: ["user:read", "user:write", "role:read", "role:write"],
      },
    },
    {
      id: 2,
      username: "testuser2",
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User2",
      role: {
        id: 2,
        name: "User",
        description: "Regular user with limited access",
        permissions: ["user:read"],
      },
    },
  ];

  const mockMutate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockConfirm.mockReset();
    mockMutate.mockReset();

    // Default mocks
    mockConfirm.mockImplementation(({ onConfirm }) => {
      if (onConfirm) onConfirm();
    });
    
    // Mock useDeleteUserMutation
    mockUseDeleteUserMutation.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    });

    mockUsePermission.mockReturnValue({
      hasPermission: (permission: string) => {
        const permissions = ["user:create", "user:edit", "user:delete"];
        return permissions.includes(permission);
      },
    });

    mockUseDeleteUserMutation.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    });

    mockUserFormModal.mockImplementation(
      ({ open, onOpenChange, user }: any) => {
        if (!open) return null;
        return (
          <div data-testid="user-form-modal">
            {user ? `Edit User: ${user.username}` : "Create User"}
            <button onClick={() => onOpenChange(false)}>Close Modal</button>
          </div>
        );
      }
    );
  });

  it("renders the table with user data", () => {
    render(<MembersTable users={mockUsers} />);

    // Check table headers by their role and position
    const headers = screen.getAllByRole('columnheader');
    expect(headers).toHaveLength(4); // User, Email, Role, Actions

    // Check user data is displayed
    mockUsers.forEach((user) => {
      expect(screen.getByText(user.username)).toBeInTheDocument();
      expect(screen.getByText(user.email)).toBeInTheDocument();
      expect(screen.getByText(user.role.name)).toBeInTheDocument();
    });
  });

  it("opens the edit modal when edit button is clicked", () => {
    render(<MembersTable users={mockUsers} />);

    // Find and click the edit button for the first user
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);

    expect(mockUserFormModal).toHaveBeenCalledWith(
      expect.objectContaining({
        open: true,
        user: mockUsers[0],
      })
    );
  });

  it("opens the edit modal when edit button is clicked", () => {
    render(<MembersTable users={mockUsers} />);

    // Find and click the edit button for the first user
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);

    expect(mockUserFormModal).toHaveBeenCalledWith(
      expect.objectContaining({
        open: true,
        user: mockUsers[0],
      })
    );
  });

  it("shows delete confirmation when delete button is clicked", () => {
    render(<MembersTable users={mockUsers} />);

    // Find and click the delete button for the first user
    const deleteButtons = screen.getAllByLabelText(/delete/i);
    fireEvent.click(deleteButtons[0]);

    // Get the first call to mockConfirm
    const confirmCall = mockConfirm.mock.calls[0][0];
    
    // Verify the structure of the confirm call
    expect(confirmCall).toMatchObject({
      title: expect.any(String),
      description: expect.any(String),
      variant: "destructive",
      confirmText: expect.any(String),
      onConfirm: expect.any(Function),
    });

    // Call the onConfirm function to simulate user confirmation
    confirmCall.onConfirm();

    // Verify the delete mutation was called with the correct user ID
    expect(mockMutate).toHaveBeenCalledWith(mockUsers[0].id);
  });

  it("disables delete button when delete mutation is pending", () => {
    // Mock the delete mutation as pending
    mockUseDeleteUserMutation.mockReturnValue({
      mutate: mockMutate,
      isPending: true,
    });

    render(<MembersTable users={mockUsers} />);

    // All delete buttons should be disabled
    const deleteButtons = screen.getAllByLabelText(/delete/i);
    deleteButtons.forEach((button) => {
      expect(button).toBeDisabled();
    });
  });

  it("respects user permissions for actions", () => {
    // Mock user without delete permission
    mockUsePermission.mockReturnValue({
      hasPermission: (permission: string) => {
        return permission !== "user:delete";
      },
    });

    render(<MembersTable users={mockUsers} />);

    // Delete buttons should not be present
    const deleteButtons = screen.queryAllByLabelText(/delete/i);
    expect(deleteButtons.length).toBe(0);

    // Edit buttons should still be present
    const editButtons = screen.getAllByLabelText(/edit/i);
    expect(editButtons.length).toBe(mockUsers.length);
  });
});
