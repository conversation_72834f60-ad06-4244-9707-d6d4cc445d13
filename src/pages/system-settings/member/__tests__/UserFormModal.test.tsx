/* eslint-disable @typescript-eslint/no-explicit-any */
import type { UserType } from "@/types/user.type.ts";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { UserFormModal } from "../components/user-form-modal";

// Mock dependencies
const mockUseCreateUserQuery = vi.fn();
const mockUseUpdateUserMutation = vi.fn();
const mockUseRoles = vi.fn();

// Mock the hooks
vi.mock("../hooks/use-create-user.query", () => ({
  useCreateUserQuery: () => mockUseCreateUserQuery(),
}));

vi.mock("../hooks/use-update-user.mutation", () => ({
  useUpdateUserMutation: () => mockUseUpdateUserMutation(),
}));

vi.mock("../hooks/use-roles", () => ({
  useRoles: () => mockUseRoles(),
}));

// Mock react-hook-form
vi.mock("react-hook-form", () => ({
  ...vi.importActual("react-hook-form"),
  useForm: vi.fn().mockReturnValue({
    register: vi.fn(),
    handleSubmit: (fn: any) => (e: any) => {
      e?.preventDefault();
      fn({
        username: "testuser",
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        password: "Test@1234",
        role: "Admin",
      });
    },
    formState: { errors: {} },
    setValue: vi.fn(),
    watch: vi.fn().mockImplementation((field: string) => {
      const values: Record<string, any> = {
        username: "testuser",
        firstName: "Test",
        lastName: "User",
        email: "<EMAIL>",
        password: "Test@1234",
        role: "Admin",
      };
      return values[field];
    }),
    reset: vi.fn(),
  }),
  Controller: ({ render }: any) =>
    render({
      field: {
        onChange: vi.fn(),
        onBlur: vi.fn(),
        value: "",
        name: "test-field",
        ref: vi.fn(),
      },
      fieldState: { error: null },
    }),
}));

// Mock react-i18next
vi.mock("react-i18next", () => ({
  useTranslation: () => {
    return {
      t: (key: string) => {
        const translations: { [key: string]: string } = {
          // Common translations
          "common:cancel": "Cancel",
          "common:save": "Save",
          "common:create": "Create",

          // Member Management translations
          "memberManagement:createUser.title": "Create User",
          "memberManagement:editUser.title": "Edit User",
          "memberManagement:createUser.form.username": "Username",
          "memberManagement:createUser.form.firstName": "First Name",
          "memberManagement:createUser.form.lastName": "Last Name",
          "memberManagement:createUser.form.email": "Email",
          "memberManagement:createUser.form.password": "Password",
          "memberManagement:createUser.form.role": "Role",
          "memberManagement:createUser.validation.username.minLength":
            "Username must be at least 3 characters",
          "memberManagement:createUser.validation.username.maxLength":
            "Username must be less than 50 characters",
          "memberManagement:createUser.validation.username.invalidCharacters":
            "Username can only contain letters, numbers, and underscores",
          "memberManagement:createUser.validation.firstName.required":
            "First name is required",
          "memberManagement:createUser.validation.lastName.required":
            "Last name is required",
          "memberManagement:createUser.validation.email.required":
            "Email is required",
          "memberManagement:createUser.validation.email.invalid":
            "Invalid email format",
          "memberManagement:createUser.validation.password.minLength":
            "Password must be at least 8 characters",
          "memberManagement:createUser.validation.password.uppercase":
            "Password must contain at least one uppercase letter",
          "memberManagement:createUser.validation.password.lowercase":
            "Password must contain at least one lowercase letter",
          "memberManagement:createUser.validation.password.number":
            "Password must contain at least one number",
          "memberManagement:createUser.validation.password.specialChar":
            "Password must contain at least one special character",
          "memberManagement:createUser.validation.role": "Role is required",
        };
        return translations[key] || key;
      },
    };
  },
}));

describe("UserFormModal", () => {
  const mockRoles = [
    { id: 1, name: "Admin" },
    { id: 2, name: "User" },
  ];

  const mockUser: UserType = {
    id: 1,
    username: "testuser",
    firstName: "Test",
    lastName: "User",
    email: "<EMAIL>",
    role: {
      id: 1,
      name: "Admin",
      description: "Administrator role",
      permissions: ["user:read", "user:write"],
    },
  };

  const mockOnSuccess = vi.fn();
  const mockOnOpenChange = vi.fn();

  const renderComponent = (props: any = {}) => {
    const defaultProps = {
      open: true,
      onOpenChange: mockOnOpenChange,
      onSuccess: mockOnSuccess,
      ...props,
    };

    return render(<UserFormModal {...defaultProps} />);
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mocks
    mockUseCreateUserQuery.mockReturnValue({
      mutate: vi.fn((_, { onSuccess }) => {
        onSuccess();
      }),
      isPending: false,
    });

    mockUseUpdateUserMutation.mockReturnValue({
      mutate: vi.fn((_, { onSuccess }) => {
        onSuccess();
      }),
      isPending: false,
    });

    mockUseRoles.mockReturnValue({
      data: mockRoles,
      isLoading: false,
    });
  });

  it("renders the create user form when no user is provided", () => {
    renderComponent();

    expect(screen.getByText("Create User")).toBeInTheDocument();
    expect(screen.getByLabelText("Username")).toBeInTheDocument();
    expect(screen.getByLabelText("First Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Last Name")).toBeInTheDocument();
    expect(screen.getByLabelText("Email")).toBeInTheDocument();
    expect(screen.getByLabelText("Password")).toBeInTheDocument();
    expect(screen.getByLabelText("Role")).toBeInTheDocument();

    // Should show create button
    expect(screen.getByRole("button", { name: /create/i })).toBeInTheDocument();
  });

  it("renders the edit user form when a user is provided", () => {
    renderComponent({ user: mockUser });

    expect(screen.getByText("Edit User")).toBeInTheDocument();

    // Password field should not be required in edit mode
    expect(screen.getByLabelText("Password").hasAttribute("required")).toBe(
      false
    );

    // Should show save button instead of create
    expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();
  });

  it("calls createUser mutation when form is submitted in create mode", async () => {
    const mockMutate = vi.fn((_, { onSuccess }) => {
      onSuccess();
    });

    mockUseCreateUserQuery.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    });

    renderComponent();

    // Submit the form
    const submitButton = screen.getByRole("button", { name: /create/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockMutate).toHaveBeenCalledWith(
        expect.objectContaining({
          username: "testuser",
          firstName: "Test",
          lastName: "User",
          email: "<EMAIL>",
          password: "Test@1234",
          role: "Admin",
        }),
        expect.any(Object)
      );

      // Should call onSuccess callback
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it("calls updateUser mutation when form is submitted in edit mode", async () => {
    const mockMutate = vi.fn((_, { onSuccess }) => {
      onSuccess();
    });

    mockUseUpdateUserMutation.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
    });

    renderComponent({ user: mockUser });

    // Submit the form
    const submitButton = screen.getByRole("button", { name: /save/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockMutate).toHaveBeenCalledWith(
        expect.objectContaining({
          id: mockUser.id,
          username: mockUser.username,
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          email: mockUser.email,
          role: mockUser.role.name,
        }),
        expect.any(Object)
      );

      // Should call onSuccess callback
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it("shows loading state when form is submitting", () => {
    mockUseCreateUserQuery.mockReturnValue({
      mutate: vi.fn(),
      isPending: true,
    });

    renderComponent();

    // Should show loading state
    expect(screen.getByRole("button", { name: /creating/i })).toBeDisabled();
  });

  it("closes the modal when cancel button is clicked", () => {
    renderComponent();

    const cancelButton = screen.getByRole("button", { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it("shows loading state when roles are loading", () => {
    mockUseRoles.mockReturnValue({
      data: [],
      isLoading: true,
    });

    renderComponent();

    // Should show loading state for roles
    expect(screen.getByText(/loading roles/i)).toBeInTheDocument();
  });

  it("shows error message when roles fail to load", () => {
    mockUseRoles.mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
      error: { message: "Failed to load roles" },
    });

    renderComponent();

    // Should show error message
    expect(screen.getByText(/failed to load roles/i)).toBeInTheDocument();
  });
});
