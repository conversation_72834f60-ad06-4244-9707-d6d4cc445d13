import { useAxios } from "@/hooks/use-axios";
import { useQuery } from "@tanstack/react-query";

interface Role {
  id: number;
  name: string;
  description?: string;
}

export function useRolesQuery() {
  const { axiosInstance } = useAxios();

  return useQuery<Role[]>({
    queryKey: ["roles"],
    queryFn: async () => {
      const response = await axiosInstance.get<Role[]>("/roles");
      return response.data;
    },
  });
}
