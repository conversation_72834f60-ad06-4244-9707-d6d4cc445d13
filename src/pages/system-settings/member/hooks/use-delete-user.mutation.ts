import { useAxios } from "@/hooks/use-axios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

export function useDeleteUserMutation() {
  const { t } = useTranslation("memberManagement");
  const queryClient = useQueryClient();
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationKey: ["deleteUser"],
    mutationFn: (userId: string | number) =>
      axiosInstance.delete(`/users/${userId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success(t("userDeletedSuccessfully"));
    },
    onError: () => {
      toast.error(t("failedToDeleteUser"));
    },
  });
}
