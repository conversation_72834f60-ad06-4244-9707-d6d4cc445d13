import { useAxios } from "@/hooks/use-axios";
import type { UserType } from "@/types/user.type";
import { useQuery } from "@tanstack/react-query";

export function useUsersQuery() {
  const { axiosInstance } = useAxios();

  return useQuery<UserType[]>({
    queryKey: ["users"],
    queryFn: async () => {
      const response = await axiosInstance.get<UserType[]>("/users");
      return response.data;
    },
  });
}
