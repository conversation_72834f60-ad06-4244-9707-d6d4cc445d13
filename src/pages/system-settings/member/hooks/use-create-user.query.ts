import { useAxios } from "@/hooks/use-axios.ts";
import type { CreateUserDataType } from "@/pages/system-settings/types/user.type.ts";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

export function useCreateUserQuery() {
  const { t } = useTranslation("memberManagement");
  const queryClient = useQueryClient();
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationKey: ["createUser"],
    mutationFn: async (userData: CreateUserDataType) => {
      const response = await axiosInstance.post("/users", {
        username: userData.username,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        roleId: userData.roleId,
        password: userData.password,
      });

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success(t("createUser.success.message"));
    },
  });
}
