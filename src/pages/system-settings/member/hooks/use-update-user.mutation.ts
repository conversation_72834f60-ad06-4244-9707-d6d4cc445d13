import { useAxios } from "@/hooks/use-axios.ts";
import type { UserDataUpdateType } from "@/pages/system-settings/types/user.type.ts";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

export function useUpdateUserMutation() {
  const { t } = useTranslation("memberManagement");
  const queryClient = useQueryClient();
  const { axiosInstance } = useAxios();

  return useMutation({
    mutationKey: ["updateUser"],
    mutationFn: (userData: UserDataUpdateType) =>
      axiosInstance.patch(`/users/${userData.id}`, {
        username: userData.username,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        roleId: userData.roleId,
        password: userData.password,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success(t("success.userUpdated"));
    },
    onError: () => {
      toast.error(t("errors.updateFailed"));
    },
  });
}
