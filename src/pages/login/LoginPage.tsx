import { useLogin } from "@/hooks/use-login";
import { validatePassword } from "@/lib/utils";
import type { AxiosError } from "axios";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function LoginPage() {
  const [usernameOrEmail, setUsernameOrEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const { t } = useTranslation("login");
  const loginMutation = useLogin();

  useEffect(() => {
    if (loginMutation.isError) {
      const error = loginMutation.error as AxiosError<{ message: string }>;
      setError(error.response?.data?.message || t("error.loginFailed"));
    }
  }, [loginMutation.error, loginMutation.isError, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!usernameOrEmail || !password) {
      setError(t("error.requiredFields"));
      return;
    }

    if (!validatePassword(password)) {
      setError(t("error.invalidPassword", { minLength: 8 }));
      return;
    }

    loginMutation.mutate({ usernameOrEmail, password });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <form
        onSubmit={handleSubmit}
        className="bg-white p-8 rounded-lg shadow-md w-full max-w-sm"
      >
        <h2 className="text-2xl font-bold mb-6 text-center">{t("title")}</h2>
        <div className="mb-4">
          <input
            type="text"
            placeholder={t("fields.usernameOrEmail")}
            value={usernameOrEmail}
            onChange={(e) => setUsernameOrEmail(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="mb-4">
          <input
            type="password"
            placeholder={t("fields.password")}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
        <button
          type="submit"
          disabled={loginMutation.isPending}
          className="w-full bg-blue-600 text-white py-2 rounded font-semibold hover:bg-blue-700 transition-colors disabled:opacity-60"
        >
          {loginMutation.isPending ? t("status.logging") : t("login")}
        </button>
        {/* Hide the register button */}
        {/* <button
          type="button"
          onClick={() => navigate("/register")}
          className="w-full mt-3 bg-gray-200 text-blue-700 py-2 rounded font-semibold hover:bg-gray-300 transition-colors"
        >
          {t("register")}
        </button> */}
      </form>
    </div>
  );
}
