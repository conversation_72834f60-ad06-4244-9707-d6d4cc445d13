import { useAxios } from "@/hooks/use-axios";
import { validatePassword } from "@/lib/utils";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";
export default function RegisterPage() {
  const { axiosInstance } = useAxios();
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const navigate = useNavigate();
  const { t } = useTranslation("register");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess("");
    setLoading(true);

    if (!username || !email || !password) {
      setError(t("error.requiredFields"));
      setLoading(false);
      return;
    }

    if (!validatePassword(password)) {
      setError(t("error.invalidPassword"));
      setLoading(false);
      return;
    }
    try {
      const res = await axiosInstance.post("auth/register", {
        username,
        email,
        password,
      });
      const data = res.data;
      if (res.status < 200 || res.status >= 300) {
        setError(data.message || t("error.registrationFailed"));
        setLoading(false);
        return;
      }
      setSuccess(t("success.registration"));
      setUsername("");
      setEmail("");
      setPassword("");
      setTimeout(() => navigate("/login"), 1500);
    } catch (err) {
      console.error("Registration error:", err);
      setError(t("error.registrationFailed"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <form
        onSubmit={handleSubmit}
        className="bg-white p-8 rounded-lg shadow-md w-full max-w-sm"
      >
        <h2 className="text-2xl font-bold mb-6 text-center">{t("title")}</h2>
        <div className="mb-4">
          <input
            type="text"
            placeholder={t("fields.username.placeholder")}
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="mb-4">
          <input
            type="email"
            placeholder={t("fields.email.placeholder")}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="mb-4">
          <input
            type="password"
            placeholder={t("fields.password.placeholder")}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        {error && <div className="text-red-500 text-sm mb-4">{error}</div>}
        {success && (
          <div className="text-green-600 text-sm mb-4">{success}</div>
        )}
        <button
          type="submit"
          disabled={loading}
          className="w-full bg-blue-600 text-white py-2 rounded font-semibold hover:bg-blue-700 transition-colors disabled:opacity-60"
        >
          {loading ? t("registering") : t("register")}
        </button>
        <button
          type="button"
          onClick={() => navigate("/login")}
          className="w-full mt-3 bg-gray-200 text-blue-700 py-2 rounded font-semibold hover:bg-gray-300 transition-colors"
        >
          {t("backToLogin")}
        </button>
      </form>
    </div>
  );
}
