import type { UserType } from "@/types/user.type";
import { create } from "zustand";

interface AuthState {
  user: UserType | null;
  isLoggedIn: boolean;
  accessToken: string | null;
}

interface AuthActions {
  setUser: (user: UserType | null) => void;
  setAccessToken: (token: string | null) => void;
  clearToken: () => void;
}

const useAuthStore = create<AuthState & AuthActions>((set) => ({
  user: null,
  isLoggedIn: false,
  accessToken: null,

  setUser: (user) => set({ user }),
  setAccessToken: (token) => set({ accessToken: token }),
  clearToken: () => set({ accessToken: null }),
}));

export default useAuthStore;
