import * as axiosHook from "@/hooks/use-axios";
import { useLogin } from "@/hooks/use-login";
import * as useAuthStoreModule from "@/stores/useAuthStore";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { act, renderHook } from "@testing-library/react";
import type { AxiosInstance } from "axios";
import * as router from "react-router";
import { beforeEach, describe, expect, it, vi } from "vitest";
const queryClient = new QueryClient();

describe("useLogin", () => {
  const mockNavigate = vi.fn();
  const mockSetToken = vi.fn();
  const mockSetUser = vi.fn();
  const mockClearToken = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(router, "useNavigate").mockReturnValue(mockNavigate);
    vi.spyOn(useAuthStoreModule, "default").mockImplementation((fn) =>
      fn({
        setAccessToken: mockSetToken,
        setUser: mockSetUser,
        clearToken: mockClearToken,
        accessToken: null,
        isLoggedIn: false,
        user: null,
      })
    );
  });
  it("logs in and sets token/user, then navigates", async () => {
    const mockAxios = {
      post: vi.fn().mockResolvedValue({
        data: {
          accessToken: "mock-token",
          user: { id: 1, name: "Test User" },
        },
      }),
      interceptors: {
        response: {
          use: vi.fn(),
        },
      },
      // Dummy methods to satisfy AxiosInstance
      get: vi.fn(),
      delete: vi.fn(),
      put: vi.fn(),
      request: vi.fn(),
      head: vi.fn(),
      options: vi.fn(),
      patch: vi.fn(),
      getUri: vi.fn(),
      defaults: {},
      create: vi.fn(),
    } as unknown as AxiosInstance;

    vi.spyOn(axiosHook, "useAxios").mockReturnValue({
      axiosInstance: mockAxios,
      accessToken: null,
    });

    const { result } = renderHook(() => useLogin(), {
      wrapper: ({ children }) =>
        QueryClientProvider({ client: queryClient, children }),
    });

    await act(() =>
      result.current.mutateAsync({
        usernameOrEmail: "<EMAIL>",
        password: "password123",
      })
    );

    expect(mockAxios.post).toHaveBeenCalledWith(
      "/auth/login",
      {
        usernameOrEmail: "<EMAIL>",
        password: "password123",
      },
      { withCredentials: true }
    );
    expect(mockSetToken).toHaveBeenCalledWith("mock-token");
    expect(mockSetUser).toHaveBeenCalledWith({ id: 1, name: "Test User" });
    expect(mockNavigate).toHaveBeenCalledWith("/");
  });
});
