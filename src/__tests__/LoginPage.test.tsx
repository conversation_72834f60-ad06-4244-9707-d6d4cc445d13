import LoginPage from "@/pages/login/LoginPage";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";

// Mock the dependencies
const mockUseLogin = vi.fn();
const mockValidatePassword = vi.fn();

// Mock the actual modules
vi.mock("@/hooks/use-login", () => ({
  useLogin: () => mockUseLogin(),
}));

vi.mock("@/lib/utils", () => ({
  validatePassword: (password: string) => mockValidatePassword(password),
}));

// Move react-i18next mock to top-level for Vitest hoisting
vi.mock("react-i18next", () => ({
  useTranslation: (namespace: string | string[] = "translation") => ({
    t: (key: string) => {
      const actualNamespace = Array.isArray(namespace)
        ? namespace[0]
        : namespace;
      const fullKey = actualNamespace ? `${actualNamespace}:${key}` : key;
      const translations: { [key: string]: string } = {
        "login:title": "Login",
        "login:fields.usernameOrEmail": "Username or Email",
        "login:fields.password": "Password",
        "login:error.requiredFields": "Please fill in all required fields.",
        "login:error.invalidPassword":
          "Password must be at least {minLength} characters long, contain at least one uppercase letter, one lowercase letter, one number, and one special character.",
        "login:error.loginFailed": "Login failed. Please try again.",
        "login:status.logging": "Logging in...",
        "login:login": "Login",
      };
      return translations[fullKey] || fullKey;
    },
  }),
}));

describe("LoginPage", () => {
  // Reset mocks before each test
  beforeEach(() => {
    vi.clearAllMocks();

    // ...existing code...

    // Default mock for useLogin (success state)
    mockUseLogin.mockReturnValue({
      mutate: vi.fn(),
      isPending: false,
      isError: false,
      error: null,
    });

    // Default mock for validatePassword (valid by default)
    mockValidatePassword.mockReturnValue(true);
  });

  it("should render the login form correctly", () => {
    render(<LoginPage />);

    expect(screen.getByRole("heading", { name: "Login" })).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText("Username or Email")
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Password")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
  });

  it("should display error message if fields are empty on submission", async () => {
    render(<LoginPage />);

    // Submit the form without filling in fields
    const form = screen.getByRole("button", { name: "Login" }).closest("form");
    if (form) {
      fireEvent.submit(form);
    } else {
      throw new Error("Form element not found.");
    }

    await waitFor(() => {
      expect(
        screen.getByText(/Please fill in all required fields./i)
      ).toBeInTheDocument();
    });

    expect(mockUseLogin().mutate).not.toHaveBeenCalled();
  });

  it("should display error message if password is invalid on submission", async () => {
    mockValidatePassword.mockReturnValue(false);

    render(<LoginPage />);

    // Type username/email
    fireEvent.change(screen.getByPlaceholderText("Username or Email"), {
      target: { value: "testuser" },
    });
    // Type password
    fireEvent.change(screen.getByPlaceholderText("Password"), {
      target: { value: "short" }, // Invalid password
    });

    // Submit the form
    fireEvent.click(screen.getByRole("button", { name: "Login" }));

    // Expect the invalid password error message
    expect(
      screen.getByText(
        "Password must be at least {minLength} characters long, contain at least one uppercase letter, one lowercase letter, one number, and one special character."
      )
    ).toBeInTheDocument();
    // Ensure mutate was NOT called
    expect(mockUseLogin().mutate).not.toHaveBeenCalled();
  });

  it("should call login mutation with correct credentials on valid submission", async () => {
    const mockMutate = vi.fn();
    mockUseLogin.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
    });

    render(<LoginPage />);

    // Type valid username/email and password
    fireEvent.change(screen.getByPlaceholderText("Username or Email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.change(screen.getByPlaceholderText("Password"), {
      target: { value: "ValidPassword123!" },
    });

    // Submit the form
    fireEvent.click(screen.getByRole("button", { name: "Login" }));

    // Expect loginMutation.mutate to be called with the correct arguments
    expect(mockMutate).toHaveBeenCalledTimes(1);
    expect(mockMutate).toHaveBeenCalledWith({
      usernameOrEmail: "<EMAIL>",
      password: "ValidPassword123!",
    });
    // Error message should not be present
    expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
  });

  it("should display 'Logging in...' and disable button when login is pending", async () => {
    const mockMutate = vi.fn();
    mockUseLogin.mockReturnValue({
      mutate: mockMutate,
      isPending: true, // Simulate pending state
      isError: false,
      error: null,
    });

    render(<LoginPage />);

    // Expect the button text to change and be disabled
    expect(
      screen.getByRole("button", { name: "Logging in..." })
    ).toBeDisabled();
  });

  it("should display error message on failed login mutation", async () => {
    const mockMutate = vi.fn();
    const mockError = {
      response: { data: { message: "Invalid credentials provided." } },
    };

    // Initial render with no error
    mockUseLogin.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
    });
    const { rerender } = render(<LoginPage />);

    // Simulate a failed login attempt by updating the mock return value
    mockUseLogin.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: true,
      error: mockError,
    });

    // Rerender the component to trigger the useEffect that watches for errors
    rerender(<LoginPage />);

    // Wait for the error message to appear
    waitFor(() => {
      expect(
        screen.getByText("Invalid credentials provided.")
      ).toBeInTheDocument();
    });
  });

  it("should display generic error message on failed login mutation if no specific message is provided", async () => {
    const mockMutate = vi.fn();
    const mockError = {
      response: { data: {} }, // No specific message
    };

    // Initial render with no error
    mockUseLogin.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: false,
      error: null,
    });
    const { rerender } = render(<LoginPage />);

    // Simulate a failed login attempt by updating the mock return value
    mockUseLogin.mockReturnValue({
      mutate: mockMutate,
      isPending: false,
      isError: true,
      error: mockError,
    });

    // Rerender the component to trigger the useEffect that watches for errors
    rerender(<LoginPage />);

    // Wait for the generic error message to appear
    waitFor(() => {
      expect(
        screen.getByText("Login failed. Please try again.")
      ).toBeInTheDocument();
    });
  });
});
