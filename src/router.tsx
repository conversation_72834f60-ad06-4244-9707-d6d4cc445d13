import ForbiddenPage from "@/pages/forbidden/ForbiddenPage.tsx";
import { createBrowserRouter } from "react-router";
import RequireAuth from "./components/require-auth";
import MainLayout from "./layouts/main-layout/MainLayout";
import ArticleListPage from "./pages/article-list/ArticleListPage";
import ComprehensiveAnalysisPage from "./pages/comprehensive-analysis/ComprehensiveAnalysisPage";
import DashboardPage from "./pages/dashboard/DashboardPage";
import LoginPage from "./pages/login/LoginPage";
import RegisterPage from "./pages/register/RegisterPage";
import ReportsPage from "./pages/reports/ReportsPage";
import SystemSettingsAccountPage from "./pages/system-settings/account/SystemSettingsAccountPage";
import SystemSettingsAnalysisPage from "./pages/system-settings/analysis/SystemSettingsAnalysisPage";
import SystemSettingsMemberPage from "./pages/system-settings/member/SystemSettingsMemberPage";
import RoleCreatePage from "./pages/system-settings/role/RoleCreatePage";
import RoleEditPage from "./pages/system-settings/role/RoleEditPage";
import SystemSettingsRolePage from "./pages/system-settings/role/SystemSettingsRolePage";
import TopicAnalysisPage from "./pages/topic-analysis/TopicAnalysisPage";
import TrendComparisonPage from "./pages/trend-comparison/TrendComparisonPage";
import TutorialPage from "./pages/tutorial/TutorialPage";

const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <RequireAuth>
        <MainLayout />
      </RequireAuth>
    ),
    children: [
      {
        index: true,
        element: <DashboardPage />,
      },
      {
        path: "comprehensive-analysis",
        element: <ComprehensiveAnalysisPage />,
      },
      {
        path: "topic-analysis",
        element: <TopicAnalysisPage />,
      },
      {
        path: "trend-comparison",
        element: <TrendComparisonPage />,
      },
      {
        path: "reports",
        element: <ReportsPage />,
      },
      {
        path: "article-list",
        element: <ArticleListPage />,
      },
      {
        path: "system-settings",
        children: [
          {
            path: "account",
            element: <SystemSettingsAccountPage />,
          },
          {
            path: "member",
            element: <SystemSettingsMemberPage />,
          },
          {
            path: "role",
            children: [
              {
                index: true,
                element: <SystemSettingsRolePage />,
              },
              {
                path: "new",
                element: <RoleCreatePage />,
              },
              {
                path: ":id/edit",
                element: <RoleEditPage />,
              },
            ],
          },
          {
            path: "analysis",
            element: <SystemSettingsAnalysisPage />,
          },
        ],
      },
      {
        path: "tutorial",
        element: <TutorialPage />,
      },
    ],
  },
  {
    path: "/login",
    element: <LoginPage />,
  },
  {
    path: "/register",
    element: <RegisterPage />,
  },
  {
    path: "/forbidden",
    element: <ForbiddenPage />,
  },
]);

export default router;
