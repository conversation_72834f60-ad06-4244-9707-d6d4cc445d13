import type React from "react";

import {
  DataSourcesSection,
  HeroSection,
  RecentAnalysisSection,
  type AnalysisRecord,
} from "@/components/dashboard";
import { useEffect, useState } from "react";

export default function MainDashboard() {
  const [analysisRecords] = useState<AnalysisRecord[]>([
    {
      id: "1",
      query: "黃建賓",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      resultCount: 30927,
      status: "completed",
      summary: "政治相關討論為主，情緒好感度為0.34",
      dateRange: "2024/06/01 至 2025/05/31",
      sentiment: {
        positive: 11,
        neutral: 56,
        negative: 33,
        goodwillScore: 0.34,
      },
      overview: {
        totalVolume: 30927,
        monthlyChange: 23.4,
        mainSource: "社群網站",
        sourcePercentage: 69.13,
      },
      wordCloud: [
        { text: "國民黨", size: 60 },
        { text: "藍委", size: 58 },
        { text: "中選會", size: 55 },
        { text: "立法院", size: 52 },
        { text: "提案", size: 48 },
        { text: "黃建賓", size: 45 },
        { text: "賴清德", size: 42 },
        { text: "還區", size: 40 },
        { text: "死刑", size: 38 },
        { text: "送件", size: 35 },
      ],
      lastUpdate: "2 分鐘前",
    },
    {
      id: "2",
      query: "Samsung Galaxy S25",
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      resultCount: 3187,
      status: "completed",
      summary: "相機功能受到好評，充電速度有待改善",
      dateRange: "2025/01/10 至 2025/01/20",
      sentiment: {
        positive: 45,
        neutral: 35,
        negative: 20,
        goodwillScore: 0.62,
      },
      overview: {
        totalVolume: 3187,
        monthlyChange: -8.7,
        mainSource: "社群媒體平台",
        sourcePercentage: 42.3,
      },
      wordCloud: [
        { text: "相機", size: 55 },
        { text: "充電", size: 50 },
        { text: "性能", size: 45 },
        { text: "價格", size: 40 },
        { text: "設計", size: 35 },
        { text: "螢幕", size: 30 },
        { text: "電池", size: 28 },
        { text: "功能", size: 25 },
        { text: "品質", size: 22 },
        { text: "推薦", size: 20 },
      ],
      lastUpdate: "5 分鐘前",
    },
    {
      id: "3",
      query: "Google Pixel 9",
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      resultCount: 1845,
      status: "completed",
      summary: "AI功能創新獲得認可，但市場可見度不足",
      dateRange: "2025/01/05 至 2025/01/18",
      sentiment: {
        positive: 72,
        neutral: 18,
        negative: 10,
        goodwillScore: 0.81,
      },
      overview: {
        totalVolume: 1845,
        monthlyChange: 23.8,
        mainSource: "評價平台",
        sourcePercentage: 38.7,
      },
      wordCloud: [
        { text: "AI", size: 58 },
        { text: "創新", size: 52 },
        { text: "功能", size: 48 },
        { text: "相機", size: 45 },
        { text: "智能", size: 42 },
        { text: "體驗", size: 38 },
        { text: "技術", size: 35 },
        { text: "品質", size: 32 },
        { text: "設計", size: 28 },
        { text: "推薦", size: 25 },
      ],
      lastUpdate: "1 分鐘前",
    },
  ]);

  // 初始化為所有主題的ID
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // 在組件加載時，設置默認選中所有主題
  useEffect(() => {
    setSelectedTopics(analysisRecords.map((record) => record.id));
  }, [analysisRecords]);

  const handleTopicToggle = (id: string) => {
    setSelectedTopics((prev) => {
      if (prev.includes(id)) {
        return prev.filter((topicId) => topicId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectedTopics.length === analysisRecords.length) {
      setSelectedTopics([]);
    } else {
      setSelectedTopics(analysisRecords.map((record) => record.id));
    }
  };

  const handleViewAnalysis = (e: React.MouseEvent, query: string) => {
    e.stopPropagation();
    window.location.href = `/topic-analysis?query=${encodeURIComponent(query)}&showResults=true`;
  };

  return (
    <div className="space-y-6">
      <HeroSection />

      <RecentAnalysisSection
        analysisRecords={analysisRecords}
        selectedTopics={selectedTopics}
        dropdownOpen={dropdownOpen}
        onDropdownToggle={() => setDropdownOpen(!dropdownOpen)}
        onTopicToggle={handleTopicToggle}
        onSelectAll={handleSelectAll}
        onViewAnalysis={handleViewAnalysis}
      />

      <DataSourcesSection />
    </div>
  );
}
