import { AdminPageSkeleton } from "@/components/admin-page-skeleton";
import usePermission from "@/hooks/use-permission";
import { useRefreshToken } from "@/hooks/use-refresh-token";
import useAuthStore from "@/stores/useAuthStore";
import { useEffect, useRef, useState } from "react";
import { Navigate, useLocation } from "react-router";

export default function RequireAuth({
  children,
}: {
  children: React.ReactNode;
}) {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { refreshToken } = useRefreshToken();
  const location = useLocation();
  const [checking, setChecking] = useState(!accessToken);
  const hasRefreshed = useRef(false);
  const { hasPermissionByPathname, isLoading } = usePermission();

  useEffect(() => {
    if (!accessToken && !hasRefreshed.current) {
      setChecking(true);
      hasRefreshed.current = true;
      refreshToken()
        .then(() => setChecking(false))
        .catch(() => setChecking(false));
    }
  }, [accessToken, refreshToken]);

  if (checking || isLoading) return <AdminPageSkeleton />;

  if (!accessToken) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!hasPermissionByPathname(location.pathname)) {
    return <Navigate to="/forbidden" state={{ from: location }} replace />;
  }

  return children;
}
