import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  FileText,
  Globe,
  Heart,
  Lightbulb,
  MessageSquare,
  Star,
  TrendingUp,
  X,
} from "lucide-react";
import { Sentiment<PERSON>ar<PERSON>hart } from "./charts/sentiment-bar-chart";
import { SourceDistributionChart } from "./charts/source-distribution-chart";
import { TopPosts } from "./charts/top-posts";
import { VolumeTrendChart } from "./charts/volume-trend-chart";
import { WordCloud } from "./charts/word-cloud";

interface ReportConfig {
  title?: string;
  description?: string;
  timeRange: {
    start: string;
    end: string;
  };
  includeCharts?: boolean;
  includeTopPosts?: boolean;
  customNotes?: string;
}

interface ReportPreviewProps {
  config: ReportConfig;
  chats: object[];
  themes: object[];
  onClose: () => void;
}

// Sample data for preview
interface SampleVolumeData {
  date: string;
  volume: number;
}

interface SampleSourceData {
  name: string;
  value: number;
  color: string;
}

const sampleVolumeData: SampleVolumeData[] = [
  { date: "01/15", volume: 245 },
  { date: "01/16", volume: 312 },
  { date: "01/17", volume: 189 },
  { date: "01/18", volume: 456 },
  { date: "01/19", volume: 378 },
  { date: "01/20", volume: 523 },
  { date: "01/21", volume: 467 },
];

const sampleSourceData: SampleSourceData[] = [
  { name: "Facebook", value: 1247, color: "#1877F2" },
  { name: "Instagram", value: 892, color: "#E4405F" },
  { name: "Twitter", value: 634, color: "#1DA1F2" },
  { name: "PTT", value: 456, color: "#FF6600" },
  { name: "新聞媒體", value: 789, color: "#34D399" },
];

const sampleSentimentData = [
  { sentiment: "正面", count: 2156, percentage: 68 },
  { sentiment: "中立", count: 697, percentage: 22 },
  { sentiment: "負面", count: 317, percentage: 10 },
];

const sampleWordData = [
  { text: "品質", size: 45 },
  { text: "服務", size: 38 },
  { text: "價格", size: 32 },
  { text: "推薦", size: 28 },
  { text: "滿意", size: 25 },
];

interface TopPost {
  id: string;
  content: string;
  author: string;
  platform: string;
  publishTime: string;
  interactions: {
    likes: number;
    shares: number;
    comments: number;
  };
  sentiment: "positive" | "negative" | "neutral";
}

const sampleTopPosts: TopPost[] = [
  {
    id: "1",
    content:
      "剛用了這個品牌的新產品，真的超級滿意！品質比預期還要好，客服也很專業，強烈推薦給大家！",
    author: "王小明",
    platform: "Facebook",
    publishTime: "2025-01-20 14:30",
    interactions: { likes: 1247, shares: 89, comments: 156 },
    sentiment: "positive",
  },
  {
    id: "2",
    content:
      "客服回應速度需要改善，等了兩天才收到回覆。產品本身還可以，但服務體驗有待加強。",
    author: "張志明",
    platform: "PTT",
    publishTime: "2025-01-18 09:15",
    interactions: { likes: 234, shares: 45, comments: 89 },
    sentiment: "negative",
  },
];

export function ReportPreview({
  config,
  chats,
  themes,
  onClose,
}: ReportPreviewProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold">報告預覽</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="flex-1 overflow-y-auto p-6 space-y-8">
          {/* I. 引言 */}
          <section>
            <div className="flex items-center space-x-2 mb-4">
              <FileText className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">I. 引言</h3>
            </div>
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold mb-3">
                  {config.title || "輿情分析報告"}
                </h4>
                <p className="text-gray-700 mb-4">
                  {config.description ||
                    "本報告針對指定時間範圍內的輿情數據進行深度分析"}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium">分析時間：</span>
                    <span className="text-gray-600">
                      {config.timeRange.start} 至 {config.timeRange.end}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">資料來源：</span>
                    <span className="text-gray-600">
                      社群媒體、新聞媒體、論壇等
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">分析對象：</span>
                    <span className="text-gray-600">
                      {chats.length + themes.length} 個分析項目
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* II. 聲量分析 */}
          <section>
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">II. 聲量分析</h3>
            </div>
            <Card>
              <CardContent className="p-6">
                <p className="text-gray-700 mb-6">
                  在分析期間內，總計監測到 4,252
                  則相關討論，整體聲量呈現穩定上升趨勢。
                  其中週末時段討論量較為活躍，平日則相對平穩。聲量高峰出現在
                  1月20日， 主要由新產品發布事件驅動。
                </p>
                {config.includeCharts && (
                  <VolumeTrendChart
                    data={sampleVolumeData}
                    timeRange="week"
                    onTimeRangeChange={() => {}}
                  />
                )}
              </CardContent>
            </Card>
          </section>

          {/* III. 來源分布 */}
          <section>
            <div className="flex items-center space-x-2 mb-4">
              <Globe className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">III. 來源分布</h3>
            </div>
            <Card>
              <CardContent className="p-6">
                <p className="text-gray-700 mb-6">
                  討論主要集中在社群媒體平台，其中 Facebook 佔比最高（29%），
                  其次為新聞媒體（19%）和 Instagram（21%）。PTT 等論壇平台
                  雖然討論量較少，但討論深度較高，值得重點關注。
                </p>
                {config.includeCharts && (
                  <SourceDistributionChart data={sampleSourceData} />
                )}
              </CardContent>
            </Card>
          </section>

          {/* IV. 情緒分析 */}
          <section>
            <div className="flex items-center space-x-2 mb-4">
              <Heart className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">IV. 情緒分析</h3>
            </div>
            <Card>
              <CardContent className="p-6">
                <p className="text-gray-700 mb-6">
                  整體情緒傾向正面，正面情緒佔 68%，中立情緒佔 22%，負面情緒佔
                  10%。
                  正面情緒主要來自產品品質讚賞和服務滿意度，負面情緒則集中在配送速度
                  和客服回應時間方面。建議重點改善服務流程以進一步提升用戶滿意度。
                </p>
                {config.includeCharts && (
                  <SentimentBarChart data={sampleSentimentData} />
                )}
              </CardContent>
            </Card>
          </section>

          {/* V. 關鍵討論議題 */}
          <section>
            <div className="flex items-center space-x-2 mb-4">
              <MessageSquare className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">V. 關鍵討論議題</h3>
            </div>
            <Card>
              <CardContent className="p-6">
                <p className="text-gray-700 mb-6">
                  主要討論話題圍繞「品質」、「服務」、「價格」等核心關鍵詞。
                  用戶對產品品質普遍滿意，服務體驗成為關注焦點，價格競爭力
                  也是重要討論點。建議持續關注服務品質提升和價格策略優化。
                </p>
                {config.includeCharts && <WordCloud words={sampleWordData} />}
              </CardContent>
            </Card>
          </section>

          {/* VI. 代表言論 */}
          {config.includeTopPosts && (
            <section>
              <div className="flex items-center space-x-2 mb-4">
                <Star className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">VI. 代表言論</h3>
              </div>
              <Card>
                <CardContent className="p-6">
                  <p className="text-gray-700 mb-6">
                    以下為分析期間內互動數最高的代表性貼文，反映了用戶的主要關注點和真實感受：
                  </p>
                  <TopPosts posts={sampleTopPosts} />
                </CardContent>
              </Card>
            </section>
          )}

          {/* VII. 結論與建議 */}
          <section>
            <div className="flex items-center space-x-2 mb-4">
              <Lightbulb className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">VII. 結論與建議</h3>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">主要發現</h4>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>品牌整體聲量呈現穩定上升趨勢，市場關注度持續提升</li>
                      <li>用戶情緒以正面為主，品牌形象良好</li>
                      <li>社群媒體是主要討論平台，需加強社群經營</li>
                      <li>服務品質是用戶關注重點，存在改善空間</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">行動建議</h4>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>優化客服流程，提升回應速度和服務品質</li>
                      <li>加強社群媒體互動，提高用戶參與度</li>
                      <li>持續監測負面情緒變化，及時應對潛在危機</li>
                      <li>定期分析競品動態，保持市場競爭優勢</li>
                    </ul>
                  </div>
                  {config.customNotes && (
                    <div>
                      <h4 className="font-semibold mb-2">補充說明</h4>
                      <p className="text-gray-700">{config.customNotes}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </section>
        </div>

        <div className="border-t p-6">
          <div className="flex justify-end space-x-4">
            <Button variant="outline" onClick={onClose}>
              關閉預覽
            </Button>
            <Button>確認並匯出</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
