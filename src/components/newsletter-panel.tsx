import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Calendar, ArrowRight } from "lucide-react";

const newsItems = [
  {
    title: "數據驅動的行銷模式：我們正走在數位行銷的路上",
    date: "2025-05-28",
    category: "產業趨勢",
  },
  {
    title: "社群媒體情感分析技術突破，準確率提升至95%以上",
    date: "2025-05-25",
    category: "技術更新",
  },
  {
    title: "品牌危機管理新策略：即時輿情監控的重要性",
    date: "2025-05-22",
    category: "案例分析",
  },
  {
    title: "跨平台數據整合分析，全方位掌握消費者聲音",
    date: "2025-05-20",
    category: "功能介紹",
  },
];

export function NewsletterPanel() {
  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-neutral-700 to-neutral-800 text-white border-0">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5" />
            <span>BONITO 洞察報告</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {newsItems.map((item, index) => (
            <div
              key={index}
              className="border-b border-neutral-600 last:border-b-0 pb-4 last:pb-0"
            >
              <div className="flex items-start space-x-3">
                <div className="bg-primary p-2 rounded-lg flex-shrink-0">
                  <FileText className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-white leading-tight mb-2">
                    {item.title}
                  </h4>
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      {item.date}
                    </span>
                    <span className="bg-neutral-600 px-2 py-1 rounded-full">
                      {item.category}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
          <Button
            variant="secondary"
            size="sm"
            className="w-full mt-4 bg-primary hover:bg-primary-600"
          >
            查看更多報告
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">今日數據概覽</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">新增討論</span>
            <span className="font-semibold text-primary">+2,847</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">情感分析</span>
            <span className="font-semibold text-secondary-600">78% 正面</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">熱門關鍵字</span>
            <span className="font-semibold text-primary-600">AI科技</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">活躍平台</span>
            <span className="font-semibold text-secondary-600">Instagram</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
