import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  ChevronDown,
  ChevronUp,
  Database,
  Filter,
  Search,
  Settings,
} from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ClusterDetailModal } from "./cluster-detail-modal";

interface BoardItem {
  id: string;
  name: string;
  description: string;
  memberCount?: number;
  postCount?: number;
}

interface SourceItem {
  id: string;
  name: string;
  description: string;
  boards?: BoardItem[];
  memberCount?: number;
}

interface Cluster {
  id: string;
  name: string;
  description: string;
  sourceCount: number;
  excludeCount: number;
  totalCount: number;
  status: "active" | "paused" | "inactive";
  sources: SourceItem[];
}

interface ClusterSelectorProps {
  selectedClusters: string[];
  onClusterChange: (clusterIds: string[]) => void;
  selectedSources?: { [clusterId: string]: string[] };
  onSourceChange?: (clusterId: string, sourceIds: string[]) => void;
  selectedBoards?: { [sourceId: string]: string[] };
  onBoardChange?: (sourceId: string, boardIds: string[]) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const availableClusters: Cluster[] = [
  {
    id: "1",
    name: "主流新聞媒體(日報用)",
    description: "包含主要新聞媒體的日報監測",
    sourceCount: 38,
    excludeCount: 0,
    totalCount: 50,
    status: "active",
    sources: [
      { id: "udn", name: "聯合新聞網", description: "台灣主要新聞媒體" },
      { id: "ltn", name: "自由時報", description: "台灣主要新聞媒體" },
      { id: "chinatimes", name: "中時新聞網", description: "台灣主要新聞媒體" },
      { id: "ettoday", name: "ETtoday", description: "台灣網路新聞媒體" },
      { id: "tvbs", name: "TVBS新聞", description: "台灣電視新聞媒體" },
    ],
  },
  {
    id: "2",
    name: "社群媒體平台",
    description: "主要社群媒體平台監測",
    sourceCount: 15,
    excludeCount: 2,
    totalCount: 50,
    status: "active",
    sources: [
      {
        id: "facebook",
        name: "Facebook",
        description: "全球最大社群媒體平台",
        memberCount: 29000000,
      },
      {
        id: "instagram",
        name: "Instagram",
        description: "圖片分享社群平台",
        memberCount: 12000000,
      },
      {
        id: "twitter",
        name: "Twitter",
        description: "微博客社群平台",
        memberCount: 8000000,
      },
      {
        id: "tiktok",
        name: "TikTok",
        description: "短影片分享平台",
        memberCount: 15000000,
      },
      {
        id: "youtube",
        name: "YouTube",
        description: "影片分享平台",
        memberCount: 20000000,
      },
    ],
  },
  {
    id: "3",
    name: "論壇討論區",
    description: "熱門論壇和討論區",
    sourceCount: 12,
    excludeCount: 1,
    totalCount: 50,
    status: "active",
    sources: [
      {
        id: "ptt",
        name: "PTT",
        description: "台灣最大BBS論壇",
        memberCount: 1500000,
        boards: [
          {
            id: "gossiping",
            name: "Gossiping八卦板",
            description: "時事討論與八卦分享",
            memberCount: 180000,
            postCount: 50000,
          },
          {
            id: "stock",
            name: "Stock股票板",
            description: "股票投資討論",
            memberCount: 120000,
            postCount: 30000,
          },
          {
            id: "c_chat",
            name: "C_Chat西洽板 / 漫畫動畫板",
            description: "ACG文化討論",
            memberCount: 95000,
            postCount: 25000,
          },
          {
            id: "hatepolitics",
            name: "HatePolitics政黑板",
            description: "政治議題討論",
            memberCount: 85000,
            postCount: 40000,
          },
          {
            id: "womentalk",
            name: "WomenTalk女孩板",
            description: "女性議題討論",
            memberCount: 75000,
            postCount: 20000,
          },
          {
            id: "baseball",
            name: "Baseball棒球板",
            description: "棒球運動討論",
            memberCount: 65000,
            postCount: 15000,
          },
          {
            id: "nba",
            name: "NBA籃球板",
            description: "NBA籃球討論",
            memberCount: 55000,
            postCount: 12000,
          },
          {
            id: "tech_job",
            name: "Tech_Job科技板",
            description: "科技業工作討論",
            memberCount: 45000,
            postCount: 8000,
          },
          {
            id: "lifeismoney",
            name: "Lifeismoney省錢板",
            description: "省錢購物討論",
            memberCount: 40000,
            postCount: 10000,
          },
          {
            id: "marriage",
            name: "Marriage婚姻板",
            description: "婚姻關係討論",
            memberCount: 35000,
            postCount: 6000,
          },
        ],
      },
      {
        id: "dcard",
        name: "Dcard",
        description: "大學生社群平台",
        memberCount: 800000,
        boards: [
          {
            id: "funny",
            name: "有趣板",
            description: "有趣內容分享",
            memberCount: 150000,
            postCount: 20000,
          },
          {
            id: "relationship",
            name: "感情板",
            description: "感情問題討論",
            memberCount: 120000,
            postCount: 18000,
          },
          {
            id: "mood",
            name: "心情板",
            description: "心情分享",
            memberCount: 100000,
            postCount: 15000,
          },
          {
            id: "work",
            name: "工作板",
            description: "職場經驗分享",
            memberCount: 90000,
            postCount: 12000,
          },
          {
            id: "beauty",
            name: "美妝板",
            description: "美妝保養討論",
            memberCount: 80000,
            postCount: 10000,
          },
          {
            id: "food",
            name: "美食板",
            description: "美食分享",
            memberCount: 70000,
            postCount: 8000,
          },
        ],
      },
      {
        id: "mobile01",
        name: "Mobile01",
        description: "3C科技討論區",
        memberCount: 600000,
        boards: [
          {
            id: "smartphone",
            name: "智慧型手機",
            description: "手機討論區",
            memberCount: 80000,
            postCount: 12000,
          },
          {
            id: "computer",
            name: "電腦討論區",
            description: "電腦硬體軟體討論",
            memberCount: 70000,
            postCount: 10000,
          },
          {
            id: "car",
            name: "汽車討論區",
            description: "汽車相關討論",
            memberCount: 60000,
            postCount: 8000,
          },
          {
            id: "camera",
            name: "相機討論區",
            description: "攝影器材討論",
            memberCount: 50000,
            postCount: 6000,
          },
        ],
      },
      {
        id: "bahamut",
        name: "巴哈姆特",
        description: "遊戲動漫討論區",
        memberCount: 500000,
        boards: [
          {
            id: "gnn",
            name: "GNN新聞",
            description: "遊戲新聞討論",
            memberCount: 100000,
            postCount: 15000,
          },
          {
            id: "c_chat2",
            name: "場外休憩區",
            description: "綜合討論區",
            memberCount: 80000,
            postCount: 20000,
          },
          {
            id: "anime",
            name: "動畫討論區",
            description: "動畫作品討論",
            memberCount: 60000,
            postCount: 8000,
          },
          {
            id: "game",
            name: "遊戲討論區",
            description: "遊戲作品討論",
            memberCount: 70000,
            postCount: 12000,
          },
        ],
      },
    ],
  },
  {
    id: "4",
    name: "評價平台",
    description: "商品和服務評價平台",
    sourceCount: 8,
    excludeCount: 0,
    totalCount: 50,
    status: "active",
    sources: [
      {
        id: "google_review",
        name: "Google評論",
        description: "Google商家評論",
      },
      { id: "app_store", name: "App Store", description: "iOS應用程式商店" },
      {
        id: "google_play",
        name: "Google Play",
        description: "Android應用程式商店",
      },
      { id: "tripadvisor", name: "Tripadvisor", description: "旅遊評價平台" },
    ],
  },
  {
    id: "5",
    name: "部落格平台",
    description: "個人部落格和專業媒體",
    sourceCount: 25,
    excludeCount: 3,
    totalCount: 50,
    status: "paused",
    sources: [
      { id: "pixnet", name: "痞客邦", description: "台灣最大部落格平台" },
      { id: "medium", name: "Medium", description: "專業寫作平台" },
      {
        id: "personal_blog",
        name: "個人部落格",
        description: "各種個人部落格",
      },
      {
        id: "professional_media",
        name: "專業媒體",
        description: "專業媒體網站",
      },
    ],
  },
];

export function ClusterSelector({
  selectedClusters,
  onClusterChange,
  selectedSources = {},
  onSourceChange,
  selectedBoards = {},
  onBoardChange,
  isCollapsed = false,
  onToggleCollapse,
}: Readonly<ClusterSelectorProps>) {
  const { t } = useTranslation("clusterSelector");
  const [searchTerm, setSearchTerm] = useState("");
  const [modalCluster, setModalCluster] = useState<Cluster | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const filteredClusters = availableClusters.filter(
    (cluster) =>
      cluster.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cluster.description.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const handleClusterToggle = (clusterId: string, checked: boolean) => {
    if (checked) {
      onClusterChange([...selectedClusters, clusterId]);
    } else {
      onClusterChange(selectedClusters.filter((id) => id !== clusterId));
      // Clear all source selections for this cluster
      if (onSourceChange) {
        onSourceChange(clusterId, []);
      }
    }
  };

  const handleClusterSettings = (cluster: Cluster) => {
    setModalCluster(cluster);
    setIsModalOpen(true);
  };

  const handleModalSourceChange = (sourceIds: string[]) => {
    if (modalCluster && onSourceChange) {
      onSourceChange(modalCluster.id, sourceIds);
    }
  };

  const handleModalBoardChange = (sourceId: string, boardIds: string[]) => {
    if (onBoardChange) {
      onBoardChange(sourceId, boardIds);
    }
  };

  const handleSelectAll = () => {
    const allClusterIds = availableClusters.map((cluster) => cluster.id);
    onClusterChange(allClusterIds);
  };

  const handleClearAll = () => {
    onClusterChange([]);
    if (onSourceChange) {
      availableClusters.forEach((cluster) => {
        onSourceChange(cluster.id, []);
      });
    }
    if (onBoardChange) {
      availableClusters.forEach((cluster) => {
        cluster.sources.forEach((source) => {
          onBoardChange(source.id, []);
        });
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "paused":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return t("status.active");
      case "paused":
        return t("status.paused");
      case "inactive":
        return t("status.inactive");
      default:
        return t("status.unknown");
    }
  };

  if (isCollapsed) {
    const totalSelectedSources = Object.values(selectedSources).reduce(
      (sum, sources) => sum + sources.length,
      0,
    );
    const totalSelectedBoards = Object.values(selectedBoards).reduce(
      (sum, boards) => sum + boards.length,
      0,
    );

    return (
      <Card className="border-primary-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Database className="w-4 h-4 text-primary" />
              <span className="font-medium text-sm">
                {t("collapsed.selectedClusters")} {selectedClusters.length}{" "}
                {t("collapsed.clusters")}
                {totalSelectedSources > 0 &&
                  ` · ${totalSelectedSources} ${t("sources")}`}
                {totalSelectedBoards > 0 &&
                  ` · ${totalSelectedBoards} ${t("boards")}`}
              </span>
            </div>
            <Button variant="ghost" size="sm" onClick={onToggleCollapse}>
              <ChevronDown className="w-4 h-4" />
            </Button>
          </div>
          {selectedClusters.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {selectedClusters.slice(0, 3).map((clusterId) => {
                const cluster = availableClusters.find(
                  (c) => c.id === clusterId,
                );
                return cluster ? (
                  <Badge
                    key={clusterId}
                    variant="secondary"
                    className="text-xs"
                  >
                    {cluster.name}
                  </Badge>
                ) : null;
              })}
              {selectedClusters.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{selectedClusters.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="border-primary-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-lg">
              <Database className="w-5 h-5 text-primary" />
              <span>{t("title")}</span>
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onToggleCollapse}>
              <ChevronUp className="w-4 h-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600">{t("description")}</p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Actions */}
          <div className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder={t("searchPlaceholder")}
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {t("selectedClusters")} {selectedClusters.length} /{" "}
                  {availableClusters.length} {t("totalClusters")}
                </Badge>
                {Object.values(selectedSources).reduce(
                  (sum, sources) => sum + sources.length,
                  0,
                ) > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {Object.values(selectedSources).reduce(
                      (sum, sources) => sum + sources.length,
                      0,
                    )}{" "}
                    {t("sources")}
                  </Badge>
                )}
                {Object.values(selectedBoards).reduce(
                  (sum, boards) => sum + boards.length,
                  0,
                ) > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {Object.values(selectedBoards).reduce(
                      (sum, boards) => sum + boards.length,
                      0,
                    )}{" "}
                    {t("boards")}
                  </Badge>
                )}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleSelectAll}>
                  {t("selectAll")}
                </Button>
                <Button variant="outline" size="sm" onClick={handleClearAll}>
                  {t("clearAll")}
                </Button>
              </div>
            </div>
          </div>

          {/* Cluster List */}
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {filteredClusters.map((cluster) => (
              <div
                key={cluster.id}
                className={`border rounded-lg p-3 transition-colors ${
                  selectedClusters.includes(cluster.id)
                    ? "border-primary-300 bg-primary-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <div className="flex items-start space-x-3">
                  <Checkbox
                    checked={selectedClusters.includes(cluster.id)}
                    onCheckedChange={(checked) =>
                      handleClusterToggle(cluster.id, checked as boolean)
                    }
                    disabled={cluster.status === "inactive"}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-sm">{cluster.name}</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={() => handleClusterSettings(cluster)}
                          disabled={!selectedClusters.includes(cluster.id)}
                          title={t("settingsTooltip")}
                        >
                          <Settings className="w-4 h-4" />
                        </Button>
                      </div>
                      <Badge
                        className={getStatusColor(cluster.status)}
                        variant="secondary"
                      >
                        {getStatusText(cluster.status)}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">
                      {cluster.description}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>
                        {t("sourceCount")}: {cluster.sourceCount} |{" "}
                        {t("excludeCount")}: {cluster.excludeCount} |{" "}
                        {t("totalCount")}: {cluster.totalCount}
                      </span>
                    </div>

                    {/* Display selected sources and boards statistics */}
                    {selectedClusters.includes(cluster.id) && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {selectedSources[cluster.id] &&
                          selectedSources[cluster.id].length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {selectedSources[cluster.id].length}{" "}
                              {t("sources")}
                            </Badge>
                          )}
                        {cluster.sources
                          .filter((source) =>
                            selectedSources[cluster.id]?.includes(source.id),
                          )
                          .reduce(
                            (sum, source) =>
                              sum + (selectedBoards[source.id]?.length || 0),
                            0,
                          ) > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            {cluster.sources
                              .filter((source) =>
                                selectedSources[cluster.id]?.includes(
                                  source.id,
                                ),
                              )
                              .reduce(
                                (sum, source) =>
                                  sum +
                                  (selectedBoards[source.id]?.length || 0),
                                0,
                              )}{" "}
                            {t("boards")}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredClusters.length === 0 && (
            <div className="text-center py-4 text-gray-500">
              <Filter className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">{t("noResults")}</p>
            </div>
          )}

          {/* Selected Summary */}
          {selectedClusters.length > 0 && (
            <div className="border-t pt-3">
              <div className="text-xs text-gray-600 mb-2">
                {t("selectedSummary")}
              </div>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-2 bg-blue-50 rounded">
                  <div className="font-medium text-blue-800">
                    {selectedClusters.length}
                  </div>
                  <div className="text-blue-600">{t("clusters")}</div>
                </div>
                <div className="text-center p-2 bg-green-50 rounded">
                  <div className="font-medium text-green-800">
                    {Object.values(selectedSources).reduce(
                      (sum, sources) => sum + sources.length,
                      0,
                    )}
                  </div>
                  <div className="text-green-600">{t("sourcesLabel")}</div>
                </div>
                <div className="text-center p-2 bg-purple-50 rounded">
                  <div className="font-medium text-purple-800">
                    {Object.values(selectedBoards).reduce(
                      (sum, boards) => sum + boards.length,
                      0,
                    )}
                  </div>
                  <div className="text-purple-600">{t("boardsLabel")}</div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal */}
      <ClusterDetailModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        cluster={modalCluster}
        selectedSources={
          modalCluster ? selectedSources[modalCluster.id] || [] : []
        }
        selectedBoards={selectedBoards}
        onSourceChange={handleModalSourceChange}
        onBoardChange={handleModalBoardChange}
        onSave={() => {
          // Additional save logic can be added here
          console.log(t("modal.saveSettings"));
        }}
      />
    </>
  );
}
