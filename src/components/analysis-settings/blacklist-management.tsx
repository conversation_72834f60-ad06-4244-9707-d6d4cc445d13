import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Download, Plus, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { type BlacklistKeyword } from "./types";

interface BlacklistManagementProps {
  readonly blacklistKeywords: BlacklistKeyword[];
}

export function BlacklistManagement({
  blacklistKeywords,
}: Readonly<BlacklistManagementProps>) {
  const { t } = useTranslation("analysisSettings");

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{t("blacklist.title")}</CardTitle>
          <p className="text-gray-600 mt-1">{t("blacklist.description")}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {t("blacklist.excelImport")}
          </Button>
          <Button className="bg-primary hover:bg-primary-600">
            <Plus className="w-4 h-4 mr-2" />
            {t("blacklist.addKeyword")}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {blacklistKeywords.map((item, index) => (
            <div
              key={index}
              className="flex justify-between items-center p-3 hover:bg-gray-50 rounded"
            >
              <div>
                <span className="font-medium">{item.keyword}</span>
                <p className="text-xs text-gray-500">{item.date}</p>
              </div>
              <Button variant="ghost" size="sm">
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
