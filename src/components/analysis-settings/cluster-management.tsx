import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Edit, Plus, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { type Cluster } from "./types";

interface ClusterManagementProps {
  readonly clusters: Cluster[];
}

export function ClusterManagement({
  clusters,
}: Readonly<ClusterManagementProps>) {
  const { t } = useTranslation("analysisSettings");

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{t("clusters.title")}</CardTitle>
          <p className="text-gray-600 mt-1">{t("clusters.description")}</p>
        </div>
        <Button className="bg-primary hover:bg-primary-600">
          <Plus className="w-4 h-4 mr-2" />
          {t("clusters.addCluster")}
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {clusters.map((cluster, index) => (
          <div
            key={index}
            className="flex justify-between items-center p-4 bg-gray-50 rounded-lg"
          >
            <div>
              <h4 className="font-medium">{cluster.name}</h4>
              <p className="text-sm text-gray-600">
                ({cluster.sourceCount})/{cluster.totalCount}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button variant="ghost" size="sm">
                <Edit className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
