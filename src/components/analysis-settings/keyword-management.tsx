import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Edit, Plus, Trash2, Upload, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { type KeywordGroup } from "./types";

interface KeywordManagementProps {
  readonly keywordGroups: KeywordGroup[];
}

export function KeywordManagement({
  keywordGroups,
}: Readonly<KeywordManagementProps>) {
  const { t } = useTranslation("analysisSettings");

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{t("keywords.title")}</CardTitle>
          <p className="text-gray-600 mt-1">{t("keywords.description")}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="w-4 h-4 mr-2" />
            {t("keywords.import")}
          </Button>
          <Button className="bg-primary hover:bg-primary-600">
            <Plus className="w-4 h-4 mr-2" />
            {t("keywords.addGroup")}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {keywordGroups.map((group) => (
          <div
            key={group.id}
            className="border rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div className="flex justify-between items-start mb-3">
              <div className="flex items-center space-x-3">
                <div className={`w-4 h-4 rounded-full ${group.color}`}></div>
                <h4 className="font-semibold">{group.name}</h4>
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4 text-gray-400" />
                  <span
                    className="text-sm text-gray-600"
                    title={t("keywords.assignees")}
                  >
                    {group.assignees.join(", ")}
                  </span>
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="ghost" size="sm">
                  <Edit className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              {group.keywords.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
