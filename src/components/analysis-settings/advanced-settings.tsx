import { useTranslation } from "react-i18next";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export function AdvancedSettings() {
  const { t } = useTranslation("analysisSettings");

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("advanced.title")}</CardTitle>
        <p className="text-gray-600">{t("advanced.description")}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">
              {t("advanced.articleCleanup.title")}
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>{t("advanced.articleCleanup.autoCleanExpired")}</span>
                <Button variant="outline" size="sm">
                  {t("advanced.enable")}
                </Button>
              </div>
              <div className="flex justify-between">
                <span>{t("advanced.articleCleanup.mergeDuplicates")}</span>
                <Button variant="outline" size="sm">
                  {t("advanced.enable")}
                </Button>
              </div>
            </div>
          </div>
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">
              {t("advanced.dataProcessing.title")}
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>{t("advanced.dataProcessing.realtimeUpdate")}</span>
                <Button variant="outline" size="sm">
                  {t("advanced.enable")}
                </Button>
              </div>
              <div className="flex justify-between">
                <span>{t("advanced.dataProcessing.sentimentAnalysis")}</span>
                <Button variant="outline" size="sm">
                  {t("advanced.enable")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
