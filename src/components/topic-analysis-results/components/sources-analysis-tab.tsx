import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/charts/horizontal-bar-chart";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Activity, Globe } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { AnalysisData } from "../types";

interface SourcesAnalysisTabProps {
  readonly analysisData: AnalysisData;
}

export function SourcesAnalysisTab({
  analysisData,
}: Readonly<SourcesAnalysisTabProps>) {
  const { t } = useTranslation("topicAnalysis", {
    keyPrefix: "results.sources",
  });

  return (
    <div className="space-y-6">
      {/* Main Media Sources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="w-5 h-5" />
            <span>{t("mainMediaSources")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {analysisData.mediaSourceCategories.map((category, index) => (
              <div
                key={index}
                className="p-4 border rounded-lg"
                style={{
                  borderColor:
                    index === 0
                      ? "#00BCD4"
                      : index === 1
                        ? "#4CAF50"
                        : index === 2
                          ? "#2196F3"
                          : "#FF9800",
                }}
              >
                <h3 className="text-lg font-bold mb-2">{category.name}</h3>
                <div
                  className="text-3xl font-bold mb-3"
                  style={{
                    color:
                      index === 0
                        ? "#00BCD4"
                        : index === 1
                          ? "#4CAF50"
                          : index === 2
                            ? "#2196F3"
                            : "#FF9800",
                  }}
                >
                  {category.percentage}%
                </div>
                <div className="flex flex-wrap gap-2">
                  {category.platforms.map((platform, idx) => (
                    <Badge
                      key={idx}
                      className="bg-gray-100 text-gray-800 hover:bg-gray-200"
                      variant="secondary"
                    >
                      {platform}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium mb-4">
              {t("mediaVolumeRanking")}
            </h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-50 text-left">
                    <th className="p-3 border-b">{t("ranking")}</th>
                    <th className="p-3 border-b">{t("mediaName")}</th>
                    <th className="p-3 border-b">{t("sentimentTendency")}</th>
                    <th className="p-3 border-b">{t("articleCount")}</th>
                  </tr>
                </thead>
                <tbody>
                  {analysisData.topMediaSources.map((source) => (
                    <tr key={source.rank} className="border-b hover:bg-gray-50">
                      <td className="p-3">{source.rank}</td>
                      <td className="p-3 font-medium">{source.name}</td>
                      <td className="p-3">
                        <Badge
                          className={
                            source.sentiment === "正面"
                              ? "bg-green-100 text-green-800"
                              : source.sentiment === "負面"
                                ? "bg-red-100 text-red-800"
                                : "bg-blue-100 text-blue-800"
                          }
                        >
                          {source.sentiment}
                        </Badge>
                      </td>
                      <td className="p-3">{source.count} 則</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Platform Activity Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>{t("platformActivity")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {analysisData.platformActivity.map((platform, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <h3 className="text-lg font-medium mb-2">{platform.name}</h3>
              <p className="text-sm text-gray-700 mb-3">
                {platform.description}
              </p>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-600">
                  {t("averageInteraction")}：
                </span>
                <Badge
                  className={
                    platform.avgInteraction > 200
                      ? "bg-green-100 text-green-800"
                      : platform.avgInteraction > 150
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                  }
                >
                  {platform.avgInteraction}
                </Badge>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <HorizontalBarChart
        data={analysisData.platformAnalysis}
        title={t("platformDistribution")}
        icon={<Globe className="w-5 h-5" />}
      />
    </div>
  );
}
