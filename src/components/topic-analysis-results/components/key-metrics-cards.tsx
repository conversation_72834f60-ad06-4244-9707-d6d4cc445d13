import { Card, CardContent } from "@/components/ui/card";
import { Globe, Heart, MessageSquare, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { AnalysisData } from "../types";

interface KeyMetricsCardsProps {
  readonly totalVolume: number;
  readonly analysisData: AnalysisData;
}

export function KeyMetricsCards({
  totalVolume,
  analysisData,
}: Readonly<KeyMetricsCardsProps>) {
  const { t } = useTranslation("topicAnalysis");

  const mainSourcePercentage = (
    (analysisData.sourceDistribution[0].value / totalVolume) *
    100
  ).toFixed(1);

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {t("results.keyMetrics.totalDiscussion")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {totalVolume.toLocaleString()}
              </p>
              <p className="text-xs text-green-600 mt-1">
                ↗ +15.3% {t("results.keyMetrics.increaseFromPrevious")}
              </p>
            </div>
            <TrendingUp className="w-8 h-8 text-primary" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {t("results.keyMetrics.sentimentRatio")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {analysisData.sentimentData.overallRatio}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {t("results.keyMetrics.positiveNegativeRatio")}
              </p>
            </div>
            <Heart className="w-8 h-8 text-red-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {t("results.keyMetrics.mainSource")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {analysisData.sourceDistribution[0].name}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {mainSourcePercentage}%{" "}
                {t("results.keyMetrics.sourcePercentage")}
              </p>
            </div>
            <Globe className="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                {t("results.keyMetrics.hotKeyword")}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {analysisData.wordCloud[1]?.text}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {t("results.keyMetrics.highestDiscussion")}
              </p>
            </div>
            <MessageSquare className="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
