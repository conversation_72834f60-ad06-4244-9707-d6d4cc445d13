import { SourceDistributionChart } from "@/components/charts/source-distribution-chart";
import { VolumeTrendChart } from "@/components/charts/volume-trend-chart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { AnalysisData } from "../types";

interface OverviewTabProps {
  readonly analysisData: AnalysisData;
  readonly totalVolume: number;
  readonly query: string;
}

export function OverviewTab({
  analysisData,
  totalVolume,
  query,
}: Readonly<OverviewTabProps>) {
  const { t } = useTranslation("topicAnalysis", {
    keyPrefix: "results.overview",
  });

  const mainSourcePercentage = (
    (analysisData.sourceDistribution[0].value / totalVolume) *
    100
  ).toFixed(1);

  return (
    <div className="space-y-6">
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <VolumeTrendChart
          data={analysisData.volumeTrend}
          timeRange="month"
          onTimeRangeChange={() => {}}
        />
        <SourceDistributionChart data={analysisData.sourceDistribution} />
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5" />
            <span>{t("analysisSummary")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">
                {t("mainFindings")}
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>
                  • 「{query}」在分析期間共產生 {totalVolume.toLocaleString()}{" "}
                  {t("totalDiscussions")}
                </li>
                <li>
                  • {t("mainPlatform")}{" "}
                  {analysisData.sourceDistribution[0].name}，佔總討論量的{" "}
                  {mainSourcePercentage}%
                </li>
                <li>
                  • {t("sentimentRatio")}{" "}
                  {analysisData.sentimentData.overallRatio}，
                  {analysisData.sentimentData.overallRatio > 1
                    ? t("positiveMore")
                    : t("negativeMore")}
                </li>
                <li>
                  • {t("hotKeyword")}「{analysisData.wordCloud[1]?.text}」
                </li>
              </ul>
            </div>

            <div className="p-4 bg-yellow-50 rounded-lg">
              <h4 className="font-semibold text-yellow-900 mb-2">
                {t("suggestedAttention")}
              </h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• {t("monitorSocialMedia")}</li>
                <li>• {t("trackSentimentChanges")}</li>
                <li>• {t("analyzeKeywords")}</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
