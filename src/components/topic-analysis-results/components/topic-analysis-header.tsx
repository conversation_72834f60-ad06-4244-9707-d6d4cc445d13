import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, BarChart3, Calendar, Download, Share } from "lucide-react";
import { useTranslation } from "react-i18next";

interface TopicAnalysisHeaderProps {
  readonly query: string;
  readonly totalVolume: number;
  readonly analysisDate: string;
  readonly onBack: () => void;
}

export function TopicAnalysisHeader({
  query,
  totalVolume,
  analysisDate,
  onBack,
}: Readonly<TopicAnalysisHeaderProps>) {
  const { t } = useTranslation("topicAnalysis", {
    keyPrefix: "results.header",
  });

  return (
    <div className="bg-white border-b p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t("back")}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              「{query}」{t("analysisResults")}
            </h1>
            <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                <span>
                  {t("analysisTime")}：{analysisDate}
                </span>
              </div>
              <div className="flex items-center">
                <BarChart3 className="w-4 h-4 mr-1" />
                <span>
                  {t("totalVolume")}：{totalVolume.toLocaleString()}{" "}
                  {t("recordsUnit")}
                </span>
              </div>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800"
              >
                {t("analysisCompleted")}
              </Badge>
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Share className="w-4 h-4 mr-2" />
            {t("share")}
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {t("exportReport")}
          </Button>
        </div>
      </div>
    </div>
  );
}
