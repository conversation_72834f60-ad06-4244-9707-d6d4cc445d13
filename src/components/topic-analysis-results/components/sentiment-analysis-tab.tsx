import { SentimentTrendChart } from "@/components/charts/sentiment-trend-chart";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Meh, ThumbsDown, ThumbsUp } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { AnalysisData, TimeRange } from "../types";

interface SentimentAnalysisTabProps {
  readonly analysisData: AnalysisData;
  readonly totalVolume: number;
  readonly timeRange: TimeRange;
  readonly onTimeRangeChange: (range: TimeRange) => void;
}

export function SentimentAnalysisTab({
  analysisData,
  totalVolume,
  timeRange,
  onTimeRangeChange,
}: Readonly<SentimentAnalysisTabProps>) {
  const { t } = useTranslation("topicAnalysis", {
    keyPrefix: "results.sentiment",
  });

  return (
    <div className="space-y-6">
      <SentimentTrendChart
        data={[]}
        timeRange={timeRange}
        onTimeRangeChange={onTimeRangeChange}
        totalPositive={analysisData.sentimentData.totalPositive}
        totalNeutral={analysisData.sentimentData.totalNeutral}
        totalNegative={analysisData.sentimentData.totalNegative}
        overallRatio={analysisData.sentimentData.overallRatio}
      />

      {/* Sentiment Trend Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>{t("sentimentTrend")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Positive Sentiment Trend */}
          <div className="p-4 bg-green-50 rounded-lg">
            <h3 className="text-lg font-medium text-green-800 mb-2">
              {t("positiveTrend")}
            </h3>
            <p className="text-sm text-gray-700 mb-2">
              正面情緒在4月中旬達到高峰，主要來自資建賓對選舉公正性的承諾和專業表現的肯定。
            </p>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-600">
                {t("positive")}
                {t("articles")}：
              </span>
              <Badge className="bg-green-100 text-green-800 text-sm">
                {analysisData.sentimentData.totalPositive.toLocaleString()}{" "}
                {t("articles")}
              </Badge>
            </div>
          </div>

          {/* Negative Sentiment Trend */}
          <div className="p-4 bg-red-50 rounded-lg">
            <h3 className="text-lg font-medium text-red-800 mb-2">
              {t("negativeTrend")}
            </h3>
            <p className="text-sm text-gray-700 mb-2">
              負面情緒在3月底有明顯上升，主要來自對中選會特定決策的質疑和批評。
            </p>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-600">
                {t("negative")}
                {t("articles")}：
              </span>
              <Badge className="bg-red-100 text-red-800 text-sm">
                {analysisData.sentimentData.totalNegative.toLocaleString()}{" "}
                {t("articles")}
              </Badge>
            </div>
          </div>

          {/* Sentiment Turning Points */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-medium text-blue-800 mb-2">
              {t("sentimentTurningPoints")}
            </h3>
            <p className="text-sm text-gray-700">
              4月15日國民黨提出修法案後，情緒出現明顯分化，支持與反對聲音同時增加，整體情緒波動較大。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Sentiment Distribution Details */}
      <Card>
        <CardHeader>
          <CardTitle>{t("sentimentDistribution")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Positive Keywords */}
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <h3 className="text-base font-medium text-green-800 mb-3">
              {t("positiveKeywords")}
            </h3>
            <div className="flex flex-wrap gap-2">
              {[
                "公正",
                "專業",
                "尊重",
                "優法",
                "公平",
                "中立",
                "理性",
                "適用",
              ].map((keyword, index) => (
                <Badge
                  key={index}
                  className="bg-green-100 text-green-800 hover:bg-green-200"
                >
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>

          {/* Neutral Keywords */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-base font-medium text-blue-800 mb-3">
              {t("neutralKeywords")}
            </h3>
            <div className="flex flex-wrap gap-2">
              {[
                "選舉",
                "中選會",
                "立法院",
                "提案",
                "審議",
                "規則",
                "程序",
                "法律",
                "修法",
              ].map((keyword, index) => (
                <Badge
                  key={index}
                  className="bg-blue-100 text-blue-800 hover:bg-blue-200"
                >
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>

          {/* Negative Keywords */}
          <div className="p-4 bg-red-50 rounded-lg border border-red-200">
            <h3 className="text-base font-medium text-red-800 mb-3">
              {t("negativeKeywords")}
            </h3>
            <div className="flex flex-wrap gap-2">
              {[
                "爭議",
                "質疑",
                "批評",
                "失職",
                "偏袒",
                "不公",
                "延宕",
                "問題",
              ].map((keyword, index) => (
                <Badge
                  key={index}
                  className="bg-red-100 text-red-800 hover:bg-red-200"
                >
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>

          {/* Sentiment Analysis Summary */}
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 className="text-base font-medium text-gray-800 mb-3">
              {t("sentimentSummary")}
            </h3>
            <p className="text-sm text-gray-700">
              「黃建賓」主題的情緒分析顯示，整體情緒好感度為
              0.34，負面情緒略高於正面情緒。正面討論主要集中在黃建賓的專業性和公正立場上，強調其依法行政的態度。負面討論則主要圍繞部分政治人物對中選會決策的質疑，以及對特定議題處理方式的批評。中立討論佔比最高，主要是客觀報導相關事件和程序。
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Sentiment Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <ThumbsUp className="w-8 h-8 text-green-500" />
              <Badge className="bg-green-100 text-green-800">
                {t("positive")}
              </Badge>
            </div>
            <div className="text-3xl font-bold text-green-600 mb-2">
              {analysisData.sentimentData.totalPositive.toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">
              {t("ofTotalDiscussion")}{" "}
              {(
                (analysisData.sentimentData.totalPositive / totalVolume) *
                100
              ).toFixed(1)}
              %
            </p>
            <div className="mt-4 text-xs text-gray-500">
              {t("mainPositiveKeywords")}：公正、專業、尊重、優法
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-gray-400">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Meh className="w-8 h-8 text-gray-500" />
              <Badge className="bg-gray-100 text-gray-800">
                {t("neutral")}
              </Badge>
            </div>
            <div className="text-3xl font-bold text-gray-600 mb-2">
              {analysisData.sentimentData.totalNeutral.toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">
              {t("ofTotalDiscussion")}{" "}
              {(
                (analysisData.sentimentData.totalNeutral / totalVolume) *
                100
              ).toFixed(1)}
              %
            </p>
            <div className="mt-4 text-xs text-gray-500">
              {t("mainNeutralKeywords")}：選舉、中選會、立法院、提案
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <ThumbsDown className="w-8 h-8 text-red-500" />
              <Badge className="bg-red-100 text-red-800">{t("negative")}</Badge>
            </div>
            <div className="text-3xl font-bold text-red-600 mb-2">
              {analysisData.sentimentData.totalNegative.toLocaleString()}
            </div>
            <p className="text-sm text-gray-600">
              {t("ofTotalDiscussion")}{" "}
              {(
                (analysisData.sentimentData.totalNegative / totalVolume) *
                100
              ).toFixed(1)}
              %
            </p>
            <div className="mt-4 text-xs text-gray-500">
              {t("mainNegativeKeywords")}：爭議、質疑、批評、失職
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
