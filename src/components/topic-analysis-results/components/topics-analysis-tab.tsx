import { WordCloud } from "@/components/charts/word-cloud";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Hash,
  MessageSquare,
  Newspaper,
  Share,
  ThumbsUp,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import type { AnalysisData } from "../types";

interface TopicsAnalysisTabProps {
  readonly analysisData: AnalysisData;
}

export function TopicsAnalysisTab({
  analysisData,
}: Readonly<TopicsAnalysisTabProps>) {
  const { t } = useTranslation("topicAnalysis", {
    keyPrefix: "results.topics",
  });

  return (
    <div className="space-y-6">
      {/* Keyword Analysis - Word Cloud */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Hash className="w-5 h-5" />
            <span>{t("keywordAnalysis")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px]">
            <WordCloud words={analysisData.wordCloud} />
          </div>
        </CardContent>
      </Card>

      {/* Hot Topics Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>{t("hotTopicsAnalysis")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {analysisData.hotTopics.map((topic, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {topic.title}
              </h3>
              <p className="text-sm text-gray-700 mb-3">{topic.description}</p>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">
                  {t("discussionPercentage")}
                </span>
                <Badge className="bg-blue-100 text-blue-800">
                  {topic.percentage}%
                </Badge>
              </div>
              <div className="w-full h-2 bg-gray-200 rounded-full mb-3">
                <div
                  className="h-2 bg-blue-500 rounded-full"
                  style={{ width: `${topic.percentage}%` }}
                ></div>
              </div>
              <div className="flex flex-wrap gap-2">
                {topic.keywords.map((keyword, idx) => (
                  <Badge key={idx} variant="outline" className="bg-white">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Hot Articles */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Newspaper className="w-5 h-5" />
            <span>{t("hotArticles")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {analysisData.hotArticles.map((article, index) => (
            <div
              key={index}
              className="p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Badge className="bg-gray-100 text-gray-800">
                    {article.source}
                  </Badge>
                  <Badge
                    className={
                      article.sentiment === "正面"
                        ? "bg-green-100 text-green-800"
                        : article.sentiment === "負面"
                          ? "bg-red-100 text-red-800"
                          : "bg-blue-100 text-blue-800"
                    }
                  >
                    {article.sentiment}
                  </Badge>
                </div>
              </div>
              <h3 className="text-base font-medium text-gray-900 mb-2">
                {article.title}
              </h3>
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div>
                  {article.publisher} • {article.date}
                </div>
                <div className="flex items-center space-x-4">
                  <span className="flex items-center">
                    <ThumbsUp className="w-4 h-4 mr-1" />
                    {article.interactions.likes}
                  </span>
                  <span className="flex items-center">
                    <Share className="w-4 h-4 mr-1" />
                    {article.interactions.shares}
                  </span>
                  <span className="flex items-center">
                    <MessageSquare className="w-4 h-4 mr-1" />
                    {article.interactions.comments}
                  </span>
                  <Badge className="bg-blue-100 text-blue-800">
                    總互動: {article.interactions.total}
                  </Badge>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Keyword Frequency Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>{t("keywordFrequency")}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {analysisData.wordCloud.slice(0, 8).map((word, index) => (
              <div
                key={index}
                className="text-center p-3 bg-gray-50 rounded-lg"
              >
                <div className="text-lg font-bold text-primary">
                  {word.text}
                </div>
                <div className="text-sm text-gray-600">
                  提及 {word.size * 10} {t("mentions")}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
