// TypeScript interfaces for Topic Analysis Results components

export interface VolumeData {
  date: string;
  volume: number;
}

export interface SourceDistributionData {
  name: string;
  value: number;
  color: string;
}

export interface SentimentData {
  totalPositive: number;
  totalNeutral: number;
  totalNegative: number;
  overallRatio: number;
}

export interface WordCloudData {
  text: string;
  size: number;
  color: string;
}

export interface TopPost {
  id: string;
  content: string;
  author: string;
  platform: string;
  publishTime: string;
  interactions: {
    likes: number;
    shares: number;
    comments: number;
  };
  sentiment: "positive" | "neutral" | "negative";
}

export interface HotTopic {
  title: string;
  percentage: number;
  description: string;
  keywords: string[];
}

export interface HotArticle {
  source: string;
  sentiment: string;
  title: string;
  publisher: string;
  date: string;
  interactions: {
    likes: number;
    shares: number;
    comments: number;
    total: number;
  };
}

export interface MediaSourceCategory {
  name: string;
  percentage: number;
  platforms: string[];
}

export interface TopMediaSource {
  rank: number;
  name: string;
  sentiment: string;
  count: number;
}

export interface PlatformActivity {
  name: string;
  description: string;
  avgInteraction: number;
}

export interface AnalysisData {
  volumeTrend: VolumeData[];
  sourceDistribution: SourceDistributionData[];
  sentimentData: SentimentData;
  wordCloud: WordCloudData[];
  topPosts: TopPost[];
  platformAnalysis: SourceDistributionData[];
  hotTopics: HotTopic[];
  hotArticles: HotArticle[];
  mediaSourceCategories: MediaSourceCategory[];
  topMediaSources: TopMediaSource[];
  platformActivity: PlatformActivity[];
  resultCount: number; // Added
  timestamp: string; // Added
}

export interface TopicAnalysisResultsProps {
  query: string;
  result: AnalysisData;
  onBack: () => void;
}

export type TimeRange = "month" | "week" | "day" | "all";

export type TabValue = "overview" | "sentiment" | "topics" | "sources";
