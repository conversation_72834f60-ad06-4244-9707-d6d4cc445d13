import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";
import {
  KeyMetricsCards,
  OverviewTab,
  SentimentAnalysisTab,
  SourcesAnalysisTab,
  TopicAnalysisHeader,
  TopicsAnalysisTab,
} from "./components";
import { useAnalysisData, useTabManagement } from "./hooks";
import type { TabValue, TopicAnalysisResultsProps } from "./types";

export function TopicAnalysisResults({
  query,
  result,
  onBack,
}: Readonly<TopicAnalysisResultsProps>) {
  const { t } = useTranslation("topicAnalysis");
  const analysisData = useAnalysisData(query);
  const { activeTab, setActiveTab, timeRange, setTimeRange } =
    useTabManagement();

  const totalVolume = result?.resultCount || 30927;
  const analysisDate = result?.timestamp
    ? new Date(result.timestamp).toLocaleDateString()
    : new Date().toLocaleDateString();

  return (
    <div className="flex-1 overflow-y-auto">
      {/* Header */}
      <TopicAnalysisHeader
        query={query}
        totalVolume={totalVolume}
        analysisDate={analysisDate}
        onBack={onBack}
      />

      {/* Content */}
      <div className="p-6">
        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as TabValue)}
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">
              {t("results.tabs.overview")}
            </TabsTrigger>
            <TabsTrigger value="sentiment">
              {t("results.tabs.sentiment")}
            </TabsTrigger>
            <TabsTrigger value="topics">{t("results.tabs.topics")}</TabsTrigger>
            <TabsTrigger value="sources">
              {t("results.tabs.sources")}
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <KeyMetricsCards
              totalVolume={totalVolume}
              analysisData={analysisData}
            />

            {/* Overview Content */}
            <OverviewTab
              analysisData={analysisData}
              totalVolume={totalVolume}
              query={query}
            />
          </TabsContent>

          {/* Sentiment Analysis Tab */}
          <TabsContent value="sentiment" className="space-y-6">
            <SentimentAnalysisTab
              analysisData={analysisData}
              totalVolume={totalVolume}
              timeRange={timeRange}
              onTimeRangeChange={setTimeRange}
            />
          </TabsContent>

          {/* Topics Analysis Tab */}
          <TabsContent value="topics" className="space-y-6">
            <TopicsAnalysisTab analysisData={analysisData} />
          </TabsContent>

          {/* Sources Analysis Tab */}
          <TabsContent value="sources" className="space-y-6">
            <SourcesAnalysisTab analysisData={analysisData} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
