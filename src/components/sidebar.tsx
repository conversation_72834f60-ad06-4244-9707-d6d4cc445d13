import { But<PERSON> } from "@/components/ui/button";
import usePermission from "@/hooks/use-permission.ts";
import { cn } from "@/lib/utils";
import PermissionEnum from "@/types/permission.enum";
import {
  BookOpen,
  Brain,
  ChevronDown,
  ChevronRight,
  Command,
  FileText,
  Home,
  Search,
  Settings,
  Target,
  TrendingUp,
  User,
  Users,
} from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router";

export function Sidebar() {
  const { t } = useTranslation("sidebar");
  const [expandedItems, setExpandedItems] = useState<string[]>([
    t("systemSettings"),
  ]);
  const pathname = useLocation().pathname;
  const navigate = useNavigate();
  const { hasPermission } = usePermission();

  const menuItems = [
    {
      icon: Home,
      label: t("dashboard"),
      href: "/",
      active: true,
      enabled: hasPermission(PermissionEnum.Dashboard),
    },
    {
      icon: Brain,
      label: t("comprehensiveAnalysis"),
      href: "/comprehensive-analysis",
    },
    { icon: Search, label: t("topicAnalysis"), href: "/topic-analysis" },
    {
      icon: TrendingUp,
      label: t("trendComparison"),
      href: "/trend-comparison",
    },
    { icon: FileText, label: t("reportsCenter"), href: "/reports" },
    { icon: FileText, label: t("articleList"), href: "/article-list" },
    {
      icon: Settings,
      label: t("systemSettings"),
      enabled: hasPermission(PermissionEnum.SystemSettings),
      submenu: [
        {
          label: t("accountSettings"),
          href: "/system-settings/account",
          icon: User,
          enabled: hasPermission(PermissionEnum.UserGetList),
        },

        {
          label: t("memberSettings"),
          href: "/system-settings/member",
          icon: Users,
          enabled: hasPermission(PermissionEnum.UserGetList),
        },
        {
          label: t("roleSettings"),
          href: "/system-settings/role",
          icon: Command,
          enabled: hasPermission(PermissionEnum.RoleGetList),
        },
        {
          label: t("analysisSettings"),
          href: "/system-settings/analysis",
          icon: Target,
          enabled: hasPermission(PermissionEnum.AnalysisList),
        },
      ],
    },
    { icon: BookOpen, label: t("tutorial"), href: "/tutorial" },
  ];

  const toggleExpanded = (label: string) => {
    setExpandedItems((prev) =>
      prev.includes(label)
        ? prev.filter((item) => item !== label)
        : [...prev, label]
    );
  };

  const isActive = (href?: string) => {
    if (!href) return false;
    return pathname === href;
  };

  const isParentActive = (submenu?: Array<{ href: string }>) => {
    if (!submenu) return false;
    return submenu.some((item) => pathname === item.href);
  };

  return (
    <aside className="w-64 bg-white border-r border-gray-200 flex flex-col h-full">
      <nav className="flex-1 p-4 space-y-2">
        {menuItems
          .filter((item) => item.enabled)
          .map((item, index) => (
            <div key={index}>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  (isActive(item.href) ||
                    (item.submenu && isParentActive(item.submenu))) &&
                    "bg-primary-50 text-primary-700 border-r-2 border-primary"
                )}
                onClick={() => {
                  if (item.submenu) {
                    toggleExpanded(item.label);
                  } else if (item.href) {
                    navigate(item.href);
                  }
                }}
              >
                <item.icon className="w-4 h-4 mr-3" />
                {item.label}
                {item.submenu && (
                  <>
                    {expandedItems.includes(item.label) ? (
                      <ChevronDown className="w-4 h-4 ml-auto" />
                    ) : (
                      <ChevronRight className="w-4 h-4 ml-auto" />
                    )}
                  </>
                )}
              </Button>
              {item.submenu && expandedItems.includes(item.label) && (
                <div className="ml-7 mt-2 space-y-1">
                  {item.submenu
                    .filter((subItem) => subItem.enabled)
                    .map((subItem, subIndex) => (
                      <Button
                        key={subIndex}
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "w-full justify-start text-sm text-gray-600",
                          isActive(subItem.href) &&
                            "bg-primary-100 text-primary-700 font-medium"
                        )}
                        onClick={() => {
                          if (subItem.href) {
                            navigate(subItem.href);
                          }
                        }}
                      >
                        {subItem.icon && (
                          <subItem.icon className="w-3 h-3 mr-2" />
                        )}
                        {subItem.label}
                      </Button>
                    ))}
                </div>
              )}
            </div>
          ))}
      </nav>
    </aside>
  );
}

export default Sidebar;
