import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FileText, Filter, Settings, Target } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  AdvancedSettings,
  BlacklistManagement,
  ClusterManagement,
  KeywordManagement,
} from "./analysis-settings/index";
import type {
  BlacklistKeyword,
  Cluster,
  KeywordGroup,
  TabType,
} from "./analysis-settings/types";
import { GradientHeader } from "./gradient-header";

const keywordGroups: KeywordGroup[] = [
  {
    id: 1,
    name: "個案_陳汶軒",
    color: "bg-green-500",
    keywords: ["陳汶軒", "性騷擾", "#MeToo", "實工民進黨"],
    assignees: ["admin", "Sabrina"],
  },
  {
    id: 2,
    name: "De<PERSON>",
    color: "bg-pink-500",
    keywords: ["Solone", "新年也要對化妝包大掃除", "知道該怎麼不同的功用間"],
    assignees: ["admin"],
  },
  {
    id: 3,
    name: "黃建富",
    color: "bg-teal-500",
    keywords: ["黃建富"],
    assignees: ["admin", "tra"],
  },
];

const clusters: Cluster[] = [
  { name: "單一頻道抓取", sourceCount: "0+1", totalCount: 50 },
  {
    name: "主流新聞媒體(日報用)_20250110",
    sourceCount: "38+0",
    totalCount: 50,
  },
  {
    name: "主流新聞媒體(週報用)_20250120",
    sourceCount: "22+0",
    totalCount: 50,
  },
];

const blacklistKeywords: BlacklistKeyword[] = [
  { keyword: "洗錢", date: "2024-08-26" },
  { keyword: "打詐", date: "2024-08-26" },
  { keyword: "詐騙", date: "2024-08-26" },
  { keyword: "詐騙集團勾結", date: "2024-08-26" },
];

export function AnalysisSettings() {
  const { t: common } = useTranslation("common");
  const { t } = useTranslation("analysisSettings");
  const [activeTab, setActiveTab] = useState<TabType>("keywords");

  return (
    <div className="space-y-6">
      {/* Header */}
      <GradientHeader
        icon={Target}
        title={t("title")}
        description={t("subtitle")}
      />

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-4">
              <nav className="space-y-2">
                <Button
                  variant={activeTab === "keywords" ? "default" : "ghost"}
                  onClick={() => setActiveTab("keywords")}
                  className="w-full justify-start"
                >
                  <Target className="w-4 h-4 mr-2" />
                  {t("keywordManagement")}
                </Button>
                <Button
                  variant={activeTab === "clusters" ? "default" : "ghost"}
                  onClick={() => setActiveTab("clusters")}
                  className="w-full justify-start"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  {t("clusterManagement")}
                </Button>
                <Button
                  variant={activeTab === "blacklist" ? "default" : "ghost"}
                  onClick={() => setActiveTab("blacklist")}
                  className="w-full justify-start"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  {t("blacklistKeywords")}
                </Button>
                <Button
                  variant={activeTab === "advanced" ? "default" : "ghost"}
                  onClick={() => setActiveTab("advanced")}
                  className="w-full justify-start"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  {common("settings")}
                </Button>
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Keywords Section */}
          {activeTab === "keywords" && (
            <KeywordManagement keywordGroups={keywordGroups} />
          )}

          {/* Clusters Section */}
          {activeTab === "clusters" && (
            <ClusterManagement clusters={clusters} />
          )}

          {/* Blacklist Section */}
          {activeTab === "blacklist" && (
            <BlacklistManagement blacklistKeywords={blacklistKeywords} />
          )}

          {/* Advanced Section */}
          {activeTab === "advanced" && <AdvancedSettings />}
        </div>
      </div>
    </div>
  );
}
