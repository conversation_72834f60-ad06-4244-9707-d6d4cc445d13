import { useEffect, useRef } from "react";

interface WordData {
  text: string;
  size: number;
  color?: string;
}

interface WordCloudProps {
  words: WordData[];
}

export function WordCloud({ words }: WordCloudProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // 設置 canvas 寬度為容器寬度
    const containerWidth = canvas.parentElement?.offsetWidth || 800;
    canvas.width = containerWidth;
    canvas.height = 400;

    // 清除畫布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 排序詞語，確保重要的詞語先放置
    const sortedWords = [...words].sort((a, b) => b.size - a.size);

    // 定義顏色方案
    const colors = [
      "#1e88e5", // 藍色
      "#43a047", // 綠色
      "#e53935", // 紅色
      "#fb8c00", // 橙色
      "#8e24aa", // 紫色
    ];

    // 已放置的詞語區域
    const placedAreas: Array<{
      x: number;
      y: number;
      width: number;
      height: number;
    }> = [];

    // 檢查是否與已放置的詞語重疊
    const checkOverlap = (
      x: number,
      y: number,
      width: number,
      height: number,
    ) => {
      // 添加邊距
      const padding = 5;
      for (const area of placedAreas) {
        if (
          x - padding < area.x + area.width &&
          x + width + padding > area.x &&
          y - padding < area.y + area.height &&
          y + height + padding > area.y
        ) {
          return true; // 重疊
        }
      }
      return false; // 不重疊
    };

    // 中心點
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // 繪製詞語
    sortedWords.forEach((word, index) => {
      // 計算字體大小
      let fontSize;
      if (index === 0) fontSize = 48;
      else if (index < 5) fontSize = 36;
      else if (index < 15) fontSize = 24;
      else if (index < 30) fontSize = 18;
      else fontSize = 14;

      // 設置字體
      ctx.font = `${fontSize}px "Microsoft JhengHei", "PingFang TC", sans-serif`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";

      // 計算詞語尺寸
      const metrics = ctx.measureText(word.text);
      const wordWidth = metrics.width;
      const wordHeight = fontSize;

      // 決定是否旋轉（只有少數詞語旋轉）
      const rotate = index > 5 && Math.random() < 0.2;

      // 實際寬高（考慮旋轉）
      const actualWidth = rotate ? wordHeight : wordWidth;
      const actualHeight = rotate ? wordWidth : wordHeight;

      // 嘗試放置詞語
      let placed = false;
      let x = 0;
      let y = 0;
      let attempts = 0;
      let radius = 0;
      let angle = 0;

      // 螺旋放置算法
      while (!placed && attempts < 1000) {
        // 螺旋參數
        angle = (attempts * 0.1) % (2 * Math.PI);
        radius = 5 + 0.5 * Math.sqrt(attempts);

        // 計算位置
        x = centerX + radius * Math.cos(angle) * 6;
        y = centerY + radius * Math.sin(angle) * 3;

        // 確保不超出邊界
        if (
          x - actualWidth / 2 > 0 &&
          x + actualWidth / 2 < canvas.width &&
          y - actualHeight / 2 > 0 &&
          y + actualHeight / 2 < canvas.height
        ) {
          // 檢查是否重疊
          if (
            !checkOverlap(
              x - actualWidth / 2,
              y - actualHeight / 2,
              actualWidth,
              actualHeight,
            )
          ) {
            placed = true;
            // 記錄已放置區域
            placedAreas.push({
              x: x - actualWidth / 2,
              y: y - actualHeight / 2,
              width: actualWidth,
              height: actualHeight,
            });
          }
        }

        attempts++;
      }

      // 如果成功放置，繪製詞語
      if (placed) {
        ctx.save();
        ctx.translate(x, y);
        if (rotate) {
          ctx.rotate(Math.PI / 2);
        }

        // 設置顏色
        ctx.fillStyle = word.color || colors[index % colors.length];

        // 繪製文字
        ctx.fillText(word.text, 0, 0);
        ctx.restore();
      }
    });
  }, [words]);

  return (
    <div className="w-full">
      <div className="w-full h-[400px] bg-white rounded-lg border border-gray-100 overflow-hidden">
        <canvas ref={canvasRef} className="w-full h-full" />
      </div>
    </div>
  );
}
