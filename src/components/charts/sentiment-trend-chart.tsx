/* eslint-disable @typescript-eslint/no-explicit-any */
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { BarChart3, Download, Heart } from "lucide-react";
import { useState } from "react";
import {
  Bar,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface SentimentTrendData {
  period: string;
  neutral: number;
  positive: number;
  negative: number;
  ratio: number;
}

interface SentimentTrendChartProps {
  data: SentimentTrendData[];
  timeRange: "month" | "week" | "day" | "all";
  onTimeRangeChange: (range: "month" | "week" | "day" | "all") => void;
  totalPositive: number;
  totalNeutral: number;
  totalNegative: number;
  overallRatio: number;
}

const monthlyData: SentimentTrendData[] = [
  {
    period: "2024/06",
    neutral: 347,
    positive: 121,
    negative: 209,
    ratio: 0.58,
  },
  {
    period: "2024/07",
    neutral: 410,
    positive: 129,
    negative: 131,
    ratio: 0.98,
  },
  { period: "2024/08", neutral: 280, positive: 56, negative: 34, ratio: 1.65 },
  { period: "2024/09", neutral: 505, positive: 284, negative: 285, ratio: 1.0 },
  { period: "2024/10", neutral: 482, positive: 95, negative: 168, ratio: 0.57 },
  { period: "2024/11", neutral: 253, positive: 105, negative: 99, ratio: 1.06 },
  {
    period: "2024/12",
    neutral: 716,
    positive: 198,
    negative: 694,
    ratio: 0.29,
  },
  {
    period: "2025/01",
    neutral: 2132,
    positive: 186,
    negative: 1197,
    ratio: 0.16,
  },
  {
    period: "2025/02",
    neutral: 1769,
    positive: 196,
    negative: 727,
    ratio: 0.27,
  },
  {
    period: "2025/03",
    neutral: 3455,
    positive: 443,
    negative: 2489,
    ratio: 0.18,
  },
  {
    period: "2025/04",
    neutral: 4167,
    positive: 637,
    negative: 2394,
    ratio: 0.27,
  },
  {
    period: "2025/05",
    neutral: 2886,
    positive: 678,
    negative: 1703,
    ratio: 0.4,
  },
];

const weeklyData: SentimentTrendData[] = [
  {
    period: "2025/05/05",
    neutral: 580,
    positive: 120,
    negative: 340,
    ratio: 0.35,
  },
  {
    period: "2025/05/12",
    neutral: 720,
    positive: 150,
    negative: 420,
    ratio: 0.36,
  },
  {
    period: "2025/05/19",
    neutral: 650,
    positive: 180,
    negative: 380,
    ratio: 0.47,
  },
  {
    period: "2025/05/26",
    neutral: 936,
    positive: 228,
    negative: 563,
    ratio: 0.4,
  },
];

const dailyData: SentimentTrendData[] = [
  { period: "05/26", neutral: 120, positive: 35, negative: 85, ratio: 0.41 },
  { period: "05/27", neutral: 180, positive: 45, negative: 120, ratio: 0.38 },
  { period: "05/28", neutral: 220, positive: 60, negative: 140, ratio: 0.43 },
  { period: "05/29", neutral: 160, positive: 40, negative: 100, ratio: 0.4 },
  { period: "05/30", neutral: 256, positive: 48, negative: 118, ratio: 0.41 },
];

export function SentimentTrendChart({
  timeRange,
  onTimeRangeChange,
  totalPositive,
  totalNeutral,
  totalNegative,
  overallRatio,
}: SentimentTrendChartProps) {
  const [showNumbers, setShowNumbers] = useState(true);

  const getCurrentData = () => {
    switch (timeRange) {
      case "week":
        return weeklyData;
      case "day":
        return dailyData;
      case "all":
        return monthlyData;
      default:
        return monthlyData;
    }
  };

  const data = getCurrentData();

  const CustomizedLabel = ({ x, y, width, value, dataKey }: any) => {
    if (!showNumbers) return null;

    let color = "#666";
    if (dataKey === "positive") color = "#10B981";
    if (dataKey === "negative") color = "#EF4444";
    if (dataKey === "neutral") color = "#3B82F6";

    return (
      <text
        x={x + width / 2}
        y={y - 5}
        fill={color}
        textAnchor="middle"
        fontSize="11"
        fontWeight="500"
      >
        {value?.toLocaleString()}
      </text>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Heart className="w-5 h-5" />
            <span>情緒</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowNumbers(!showNumbers)}
              className="h-8 px-3"
            >
              <BarChart3 className="w-4 h-4 mr-1" />
              {showNumbers ? "隱藏" : "顯示"}數據
            </Button>
            <Button variant="outline" size="sm" className="h-8 px-3">
              <Download className="w-4 h-4 mr-1" />
              下載
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-700">
            「黃建賓」情緒好感度為{" "}
            <span className="font-semibold">{overallRatio}</span>， 正面聲量則數{" "}
            <span className="font-semibold text-green-600">
              {totalPositive.toLocaleString()}
            </span>{" "}
            則， 負面聲量則數{" "}
            <span className="font-semibold text-red-600">
              {totalNegative.toLocaleString()}
            </span>{" "}
            則。
          </p>
        </div>

        {/* Time Range Buttons */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Button
              variant={timeRange === "month" ? "default" : "outline"}
              size="sm"
              onClick={() => onTimeRangeChange("month")}
              className="h-8 px-3"
            >
              月
            </Button>
            <Button
              variant={timeRange === "week" ? "default" : "outline"}
              size="sm"
              onClick={() => onTimeRangeChange("week")}
              className="h-8 px-3"
            >
              週
            </Button>
            <Button
              variant={timeRange === "day" ? "default" : "outline"}
              size="sm"
              onClick={() => onTimeRangeChange("day")}
              className="h-8 px-3"
            >
              日
            </Button>
            <Button
              variant={timeRange === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => onTimeRangeChange("all")}
              className="h-8 px-3"
            >
              全期間
            </Button>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              數據
            </Badge>
          </div>
        </div>

        {/* Chart */}
        <div className="w-full">
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart
              data={data}
              margin={{ top: 40, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="period"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
              />
              <YAxis
                yAxisId="left"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
                tickFormatter={(value) => value.toLocaleString()}
              />
              <YAxis
                yAxisId="right"
                orientation="right"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: "#666" }}
                domain={[0, 5]}
              />
              <Tooltip />
              <Legend wrapperStyle={{ paddingTop: "20px" }} iconType="rect" />

              <Bar
                yAxisId="left"
                dataKey="neutral"
                fill="#3B82F6"
                name="中立則數"
                radius={[2, 2, 0, 0]}
              >
                {showNumbers &&
                  data.map((_, index) => (
                    <CustomizedLabel key={index} dataKey="neutral" />
                  ))}
              </Bar>

              <Bar
                yAxisId="left"
                dataKey="positive"
                fill="#10B981"
                name="正面則數"
                radius={[2, 2, 0, 0]}
              >
                {showNumbers &&
                  data.map((_, index) => (
                    <CustomizedLabel key={index} dataKey="positive" />
                  ))}
              </Bar>

              <Bar
                yAxisId="left"
                dataKey="negative"
                fill="#EF4444"
                name="負面則數"
                radius={[2, 2, 0, 0]}
              >
                {showNumbers &&
                  data.map((_, index) => (
                    <CustomizedLabel key={index} dataKey="negative" />
                  ))}
              </Bar>

              <Line
                yAxisId="right"
                type="monotone"
                dataKey="ratio"
                stroke="#1F2937"
                strokeWidth={2}
                name="正負面情緒比"
                dot={{ fill: "#1F2937", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#1F2937", strokeWidth: 2 }}
              />
            </ComposedChart>
          </ResponsiveContainer>
        </div>

        {/* Statistics Summary */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {totalNeutral.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">中立情緒</div>
            <div className="text-xs text-gray-500">
              {(
                (totalNeutral /
                  (totalNeutral + totalPositive + totalNegative)) *
                100
              ).toFixed(1)}
              %
            </div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {totalPositive.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">正面情緒</div>
            <div className="text-xs text-gray-500">
              {(
                (totalPositive /
                  (totalNeutral + totalPositive + totalNegative)) *
                100
              ).toFixed(1)}
              %
            </div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">
              {totalNegative.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">負面情緒</div>
            <div className="text-xs text-gray-500">
              {(
                (totalNegative /
                  (totalNeutral + totalPositive + totalNegative)) *
                100
              ).toFixed(1)}
              %
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-800">
              {overallRatio}
            </div>
            <div className="text-sm text-gray-600">情緒好感度</div>
            <div className="text-xs text-gray-500">正負面比值</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
