import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ExternalLink,
  MessageCircle,
  Share,
  Star,
  ThumbsUp,
} from "lucide-react";

interface PostData {
  id: string;
  content: string;
  author: string;
  platform: string;
  publishTime: string;
  interactions: {
    likes: number;
    shares: number;
    comments: number;
  };
  sentiment: "positive" | "neutral" | "negative";
  url?: string;
}

interface TopPostsProps {
  posts: PostData[];
}

export function TopPosts({ posts }: TopPostsProps) {
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "positive":
        return "bg-green-100 text-green-800";
      case "negative":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSentimentText = (sentiment: string) => {
    switch (sentiment) {
      case "positive":
        return "正面";
      case "negative":
        return "負面";
      default:
        return "中立";
    }
  };

  const getPlatformIcon = (platform: string) => {
    console.log(platform);
    // You can add specific platform icons here
    return <MessageCircle className="w-4 h-4" />;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Star className="w-5 h-5" />
          <span>Top 影響力貼文</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {posts.map((post, index) => (
            <Card key={post.id} className="border-l-4 border-l-primary">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-primary">
                      #{index + 1}
                    </span>
                    <div className="flex items-center space-x-2">
                      {getPlatformIcon(post.platform)}
                      <span className="text-sm font-medium">
                        {post.platform}
                      </span>
                    </div>
                    <Badge className={getSentimentColor(post.sentiment)}>
                      {getSentimentText(post.sentiment)}
                    </Badge>
                  </div>
                  {post.url && (
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  )}
                </div>

                <div className="mb-3">
                  <p className="text-gray-800 line-clamp-3">{post.content}</p>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-600">
                  <div>
                    <span className="font-medium">{post.author}</span>
                    <span className="mx-2">•</span>
                    <span>{post.publishTime}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-6 mt-3 pt-3 border-t">
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <ThumbsUp className="w-4 h-4" />
                    <span>{post.interactions.likes.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <Share className="w-4 h-4" />
                    <span>{post.interactions.shares.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center space-x-1 text-sm text-gray-600">
                    <MessageCircle className="w-4 h-4" />
                    <span>{post.interactions.comments.toLocaleString()}</span>
                  </div>
                  <div className="ml-auto text-sm font-medium text-primary">
                    總互動:{" "}
                    {(
                      post.interactions.likes +
                      post.interactions.shares +
                      post.interactions.comments
                    ).toLocaleString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
