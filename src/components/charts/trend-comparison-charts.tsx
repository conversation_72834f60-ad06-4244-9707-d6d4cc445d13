import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Bar<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>hartI<PERSON>, TrendingUp } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON><PERSON>,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface TrendData {
  date: string;
  [key: string]: string | number;
}

interface TopicVolume {
  topic: string;
  volume: number;
  color: string;
}

interface TrendComparisonChartsProps {
  selectedTopics: string[];
  topicNames: { [key: string]: string };
  period1Data: TrendData[];
  period2Data: TrendData[];
  volumeData: TopicVolume[];
}

const COLORS = [
  "#00B2B2",
  "#FF6B6B",
  "#4ECDC4",
  "#45B7D1",
  "#96CEB4",
  "#FFEAA7",
  "#DDA0DD",
  "#98D8C8",
];

export function TrendComparisonCharts({
  selectedTopics,
  topicNames,
  period1Data,
  period2Data,
  volumeData,
}: Readonly<TrendComparisonChartsProps>) {
  const { t } = useTranslation("trendComparison");
  const [timeUnit, setTimeUnit] = useState<"day" | "week" | "month">("day");
  const [showDataPoints, setShowDataPoints] = useState(true);
  const [showAverageLine, setShowAverageLine] = useState(false);

  // 計算平均值
  const calculateAverage = (data: TrendData[], topic: string) => {
    const values = data.map((d) => Number(d[topic]) || 0);
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  };

  return (
    <div className="space-y-6">
      {/* 聲量統計長條圖 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-primary" />
            <span>{t("charts.volumeBarChart")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={volumeData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="topic" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="volume" fill="#00B2B2" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* 聲量統計圓餅圖 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChartIcon className="w-5 h-5 text-primary" />
            <span>{t("charts.volumePieChart")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-8">
            <div className="flex-1">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={volumeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ topic, percent }) =>
                      `${topic} ${((percent || 0) * 100).toFixed(1)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="volume"
                  >
                    {volumeData.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="space-y-2">
              {volumeData.map((item, index) => (
                <div key={item.topic} className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className="text-sm">{item.topic}</span>
                  <Badge variant="outline" className="text-xs">
                    {item.volume.toLocaleString()}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 聲量趨勢比較圖 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-primary" />
              <span>{t("charts.trendComparisonChart")}</span>
            </CardTitle>
            <div className="flex items-center space-x-4">
              {/* 時間單位切換 */}
              <div className="flex space-x-2">
                <Button
                  variant={timeUnit === "day" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeUnit("day")}
                >
                  {t("day")}
                </Button>
                <Button
                  variant={timeUnit === "week" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeUnit("week")}
                >
                  {t("week")}
                </Button>
                <Button
                  variant={timeUnit === "month" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setTimeUnit("month")}
                >
                  {t("month")}
                </Button>
              </div>

              {/* 顯示選項 */}
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showDataPoints}
                    onCheckedChange={setShowDataPoints}
                  />
                  <Eye className="w-4 h-4" />
                  <span className="text-sm">{t("charts.showDataPoints")}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showAverageLine}
                    onCheckedChange={setShowAverageLine}
                  />
                  <Minus className="w-4 h-4" />
                  <span className="text-sm">{t("charts.averageLine")}</span>
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart
              data={[
                ...period1Data.map((item) => ({
                  ...item,
                  period: t("charts.period1Label"),
                  date: `${t("charts.period1Label")}-${item.date}`,
                })),
                ...(period2Data.length > 0
                  ? period2Data.map((item) => ({
                      ...item,
                      period: t("charts.period2Label"),
                      date: `${t("charts.period2Label")}-${item.date}`,
                    }))
                  : []),
              ]}
              margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                angle={-45}
                textAnchor="end"
                height={80}
                interval={0}
              />
              <YAxis />
              <Tooltip />
              <Legend />
              {selectedTopics.map((topicId, index) => (
                <Line
                  key={topicId}
                  type="monotone"
                  dataKey={topicId}
                  stroke={COLORS[index % COLORS.length]}
                  strokeWidth={2}
                  dot={
                    showDataPoints
                      ? { fill: COLORS[index % COLORS.length] }
                      : false
                  }
                  name={topicNames[topicId] || topicId}
                />
              ))}
              {showAverageLine &&
                selectedTopics.map((topicId, index) => (
                  <ReferenceLine
                    key={`avg-${topicId}`}
                    y={calculateAverage(
                      [...period1Data, ...period2Data],
                      topicId,
                    )}
                    stroke={COLORS[index % COLORS.length]}
                    strokeDasharray="5 5"
                    strokeOpacity={0.7}
                  />
                ))}
            </LineChart>
          </ResponsiveContainer>

          {/* 圖例說明 */}
          <div className="mt-4 flex flex-wrap gap-4">
            {selectedTopics.map((topicId, index) => (
              <div key={topicId} className="flex items-center space-x-2">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                />
                <span className="text-sm">
                  {topicNames[topicId] || topicId}
                </span>
              </div>
            ))}
          </div>

          {period2Data.length > 0 && (
            <div className="mt-2 text-sm text-gray-600">
              <span className="font-medium">{t("charts.period1Label")}:</span>{" "}
              {t("charts.period1Description")} |{" "}
              <span className="font-medium ml-2">
                {t("charts.period2Label")}:
              </span>{" "}
              {t("charts.period2Description")}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
