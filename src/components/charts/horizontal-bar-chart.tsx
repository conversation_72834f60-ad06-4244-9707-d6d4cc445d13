import type React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface HorizontalBarData {
  name: string;
  value: number;
  color: string;
}

interface HorizontalBarChartProps {
  data: HorizontalBarData[];
  title: string;
  icon?: React.ReactNode;
}

export function HorizontalBarChart({
  data,
  title,
  icon,
}: HorizontalBarChartProps) {
  // 按數值排序，從大到小
  const sortedData = [...data].sort((a, b) => b.value - a.value);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          {icon}
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {sortedData.map((item, index) => (
            <div key={index} className="flex items-center space-x-3">
              <Badge
                variant="outline"
                className="min-w-[24px] h-6 flex items-center justify-center text-xs"
              >
                {index + 1}
              </Badge>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium truncate">
                    {item.name}
                  </span>
                  <span className="text-xs text-gray-500 ml-2">
                    {item.value.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${(item.value / sortedData[0].value) * 100}%`,
                      backgroundColor: item.color,
                    }}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 統計摘要 */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">總計項目：</span>
              <span className="font-medium">{sortedData.length}</span>
            </div>
            <div>
              <span className="text-gray-600">總聲量：</span>
              <span className="font-medium">
                {sortedData
                  .reduce((sum, item) => sum + item.value, 0)
                  .toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
