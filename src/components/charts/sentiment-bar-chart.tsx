import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Heart } from "lucide-react";
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";

interface SentimentData {
  sentiment: string;
  count: number;
  percentage: number;
}

interface SentimentBarChartProps {
  data: SentimentData[];
}

export function SentimentBarChart({ data }: SentimentBarChartProps) {
  const getBarColor = (sentiment: string) => {
    switch (sentiment) {
      case "正面":
        return "#10B981";
      case "中立":
        return "#6B7280";
      case "負面":
        return "#EF4444";
      default:
        return "#00B2B2";
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Heart className="w-5 h-5" />
          <span>情緒分析</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="sentiment" />
            <YAxis />
            <Tooltip
              formatter={(value) => [
                `${value} 則 (${data.find((d) => d.count === value)?.percentage}%)`,
                "數量",
              ]}
            />
            <Bar dataKey="count" fill="#00B2B2" radius={[4, 4, 0, 0]}>
              {data.map((entry, index) => (
                <Bar key={`bar-${index}`} fill={getBarColor(entry.sentiment)} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
        <div className="mt-4 grid grid-cols-3 gap-4">
          {data.map((item, index) => (
            <div key={index} className="text-center">
              <div
                className="text-2xl font-bold"
                style={{ color: getBarColor(item.sentiment) }}
              >
                {item.count.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">{item.sentiment}</div>
              <div className="text-xs text-gray-500">{item.percentage}%</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
