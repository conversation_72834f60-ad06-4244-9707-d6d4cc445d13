import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { Button } from "@/components/ui/button";
import { TrendingUp } from "lucide-react";

interface VolumeTrendData {
  date: string;
  volume: number;
}

interface VolumeTrendChartProps {
  data: VolumeTrendData[];
  timeRange: "day" | "week" | "month";
  onTimeRangeChange: (range: "day" | "week" | "month") => void;
}

export function VolumeTrendChart({
  data,
  timeRange,
  onTimeRangeChange,
}: VolumeTrendChartProps) {
  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <TrendingUp className="w-5 h-5" />
          <span className="font-medium">聲量趨勢圖</span>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={timeRange === "day" ? "default" : "outline"}
            size="sm"
            onClick={() => onTimeRangeChange("day")}
          >
            日
          </Button>
          <Button
            variant={timeRange === "week" ? "default" : "outline"}
            size="sm"
            onClick={() => onTimeRangeChange("week")}
          >
            週
          </Button>
          <Button
            variant={timeRange === "month" ? "default" : "outline"}
            size="sm"
            onClick={() => onTimeRangeChange("month")}
          >
            月
          </Button>
        </div>
      </div>
      <div className="w-full">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={data} width={500} height={300}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="volume"
              stroke="#00B2B2"
              strokeWidth={2}
              dot={{ fill: "#00B2B2" }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
