import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Play, TrendingUp } from "lucide-react";
import { useState } from "react";
import type { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";
import { TrendComparisonCharts } from "./charts/trend-comparison-charts";
import { ClusterSelector } from "./cluster-selector";
import { TimeRangeSelector } from "./time-range-selector";
import { TopicSelector } from "./topic-selector";

// 模擬數據
interface MockDataEntry {
  date: string;
  [key: string]: string | number; // Allows for dynamic topic keys
}

const generateMockData = (topics: string[], days = 30): MockDataEntry[] => {
  const data: MockDataEntry[] = [];
  for (let i = 0; i < days; i++) {
    const date = new Date();
    date.setDate(date.getDate() - (days - 1 - i));
    const entry: MockDataEntry = {
      date: date.toISOString().split("T")[0],
    };
    topics.forEach((topic) => {
      entry[topic] = Math.floor(Math.random() * 1000) + 100;
    });
    data.push(entry);
  }
  return data;
};

const generateVolumeData = (
  topics: string[],
  topicNames: { [key: string]: string },
) => {
  return topics.map((topic) => ({
    topic: topicNames[topic] || topic,
    volume: Math.floor(Math.random() * 5000) + 500,
    color: "#00B2B2",
  }));
};

export function TrendComparison() {
  const { t } = useTranslation("trendComparison");
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [period1, setPeriod1] = useState<DateRange | undefined>();
  const [period2, setPeriod2] = useState<DateRange | undefined>();
  const [selectedClusters, setSelectedClusters] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<{
    [clusterId: string]: string[];
  }>({});
  const [selectedBoards, setSelectedBoards] = useState<{
    [sourceId: string]: string[];
  }>({});
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [hasResults, setHasResults] = useState(false);

  // 主題名稱映射
  const topicNames: { [key: string]: string } = {
    "1": t("topicNames.electricVehicles"),
    "2": t("topicNames.artificialIntelligence"),
    "3": t("topicNames.housingPolicy"),
    "4": t("topicNames.postPandemicRecovery"),
    "5": t("topicNames.greenEnergyPolicy"),
    "6": t("topicNames.digitalTransformation"),
  };

  const canAnalyze =
    selectedTopics.length >= 2 &&
    selectedTopics.length <= 5 &&
    period1 &&
    selectedClusters.length > 0;

  const handleAnalyze = async () => {
    if (!canAnalyze) return;

    setIsAnalyzing(true);
    // 模擬分析過程
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsAnalyzing(false);
    setHasResults(true);
  };

  const mockPeriod1Data = generateMockData(selectedTopics, 30);
  const mockPeriod2Data = generateMockData(selectedTopics, 30);
  const mockVolumeData = generateVolumeData(selectedTopics, topicNames);

  return (
    <div className="space-y-6">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center space-x-2">
            <TrendingUp className="w-6 h-6 text-primary" />
            <span>{t("title")}</span>
          </h1>
          <p className="text-gray-600 mt-1">{t("subtitle")}</p>
        </div>
        <Button
          onClick={handleAnalyze}
          disabled={!canAnalyze || isAnalyzing}
          className="flex items-center space-x-2"
        >
          <Play className="w-4 h-4" />
          <span>{isAnalyzing ? t("analyzing") : t("startAnalysis")}</span>
        </Button>
      </div>

      {/* 分析設定區域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 主題選擇 */}
        <TopicSelector
          selectedTopics={selectedTopics}
          onTopicChange={setSelectedTopics}
        />

        {/* 時間區間選擇 */}
        <TimeRangeSelector
          period1={period1}
          period2={period2}
          onPeriod1Change={setPeriod1}
          onPeriod2Change={setPeriod2}
        />
      </div>

      {/* 資料源選擇 */}
      <ClusterSelector
        selectedClusters={selectedClusters}
        onClusterChange={setSelectedClusters}
        selectedSources={selectedSources}
        onSourceChange={(clusterId, sourceIds) => {
          setSelectedSources((prev) => ({ ...prev, [clusterId]: sourceIds }));
        }}
        selectedBoards={selectedBoards}
        onBoardChange={(sourceId, boardIds) => {
          setSelectedBoards((prev) => ({ ...prev, [sourceId]: boardIds }));
        }}
      />

      {/* 分析狀態提示 */}
      {!canAnalyze && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  {t("requirements.title")}
                </p>
                <ul className="text-xs text-yellow-700 mt-1 space-y-1">
                  {selectedTopics.length < 2 && (
                    <li>• {t("requirements.minTopics")}</li>
                  )}
                  {selectedTopics.length > 5 && (
                    <li>• {t("requirements.maxTopics")}</li>
                  )}
                  {!period1 && <li>• {t("requirements.period1Required")}</li>}
                  {selectedClusters.length === 0 && (
                    <li>• {t("requirements.clustersRequired")}</li>
                  )}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 分析結果 */}
      {hasResults && selectedTopics.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">{t("analysisResults")}</h2>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">
                {selectedTopics.length} {t("topicsCount")}
              </Badge>
              <Badge variant="outline">
                {selectedClusters.length} {t("clustersCount")}
              </Badge>
            </div>
          </div>

          <TrendComparisonCharts
            selectedTopics={selectedTopics}
            topicNames={topicNames}
            period1Data={mockPeriod1Data}
            period2Data={mockPeriod2Data}
            volumeData={mockVolumeData}
          />
        </div>
      )}
    </div>
  );
}
