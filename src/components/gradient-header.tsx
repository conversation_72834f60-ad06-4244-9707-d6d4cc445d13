import type { LucideIcon } from "lucide-react";

interface GradientHeaderProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
}

export function GradientHeader({
  icon: Icon,
  title,
  description,
  className = "",
}: GradientHeaderProps) {
  return (
    <div className={`bg-gradient-to-r from-primary-400 to-secondary-400 text-white rounded-lg p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-2">
        <Icon className="w-6 h-6" />
        <h1 className="text-2xl font-bold">{title}</h1>
      </div>
      <p className="text-white/90">{description}</p>
    </div>
  );
}
