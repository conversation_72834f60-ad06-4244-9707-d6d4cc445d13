import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { ChevronDown, ChevronUp, Search, Target, Users } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

interface KeywordGroup {
  id: number;
  name: string;
  color: string;
  keywords: string[];
  assignees: string[];
}

interface KeywordSelectorProps {
  selectedKeywordGroups: number[];
  onKeywordGroupChange: (groupIds: number[]) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

// 從分析設定中獲取的關鍵字群組數據
const keywordGroups: KeywordGroup[] = [
  {
    id: 1,
    name: "個案_陳汶軒",
    color: "bg-green-500",
    keywords: [
      "陳汶軒",
      "性騷擾",
      "#MeToo",
      "實工民進黨",
      "陳汶軒",
      "性騷擾",
      "#MeToo",
      "實工民進黨",
    ],
    assignees: ["admin", "Sabrina"],
  },
  {
    id: 2,
    name: "Demi",
    color: "bg-pink-500",
    keywords: [
      "Solone",
      "新年也要對化妝包大掃除",
      "知道該怎麼不同的功用間",
      "新年心願印刷即將有紅潮好氣色",
    ],
    assignees: ["admin"],
  },
  {
    id: 3,
    name: "黃建富",
    color: "bg-teal-500",
    keywords: ["黃建富"],
    assignees: ["admin", "tra"],
  },
  {
    id: 4,
    name: "食藥安全",
    color: "bg-orange-500",
    keywords: [
      "Haribol小熊軟糖",
      "大血腸",
      "食品良好衛生規範準則",
      "新冠疫苗接種",
    ],
    assignees: ["admin", "bdc2", "bdc3"],
  },
];

export function KeywordSelector({
  selectedKeywordGroups,
  onKeywordGroupChange,
  isCollapsed = false,
  onToggleCollapse,
}: Readonly<KeywordSelectorProps>) {
  const { t } = useTranslation("keywordSelector");
  const [searchTerm, setSearchTerm] = useState("");

  const filteredKeywordGroups = keywordGroups.filter(
    (group) =>
      group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.keywords.some((keyword) =>
        keyword.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
  );

  const handleKeywordGroupToggle = (groupId: number, checked: boolean) => {
    if (checked) {
      onKeywordGroupChange([...selectedKeywordGroups, groupId]);
    } else {
      onKeywordGroupChange(
        selectedKeywordGroups.filter((id) => id !== groupId),
      );
    }
  };

  const handleSelectAll = () => {
    onKeywordGroupChange(keywordGroups.map((group) => group.id));
  };

  const handleClearAll = () => {
    onKeywordGroupChange([]);
  };

  if (isCollapsed) {
    return (
      <Card className="border-primary-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-primary" />
              <span className="font-medium text-sm">
                {t("collapsed.selectedGroups")} {selectedKeywordGroups.length}{" "}
                {t("collapsed.groups")}
              </span>
            </div>
            <Button variant="ghost" size="sm" onClick={onToggleCollapse}>
              <ChevronDown className="w-4 h-4" />
            </Button>
          </div>
          {selectedKeywordGroups.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {selectedKeywordGroups.slice(0, 3).map((groupId) => {
                const group = keywordGroups.find((g) => g.id === groupId);
                return group ? (
                  <Badge key={groupId} variant="secondary" className="text-xs">
                    {group.name}
                  </Badge>
                ) : null;
              })}
              {selectedKeywordGroups.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{selectedKeywordGroups.length - 3}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-primary-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-lg">
            <Target className="w-5 h-5 text-primary" />
            <span>{t("title")}</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onToggleCollapse}>
            <ChevronUp className="w-4 h-4" />
          </Button>
        </div>
        <p className="text-sm text-gray-600">{t("description")}</p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Actions */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder={t("searchPlaceholder")}
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">
                {t("selectedCount")} {selectedKeywordGroups.length} /{" "}
                {keywordGroups.length}
              </Badge>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                {t("selectAll")}
              </Button>
              <Button variant="outline" size="sm" onClick={handleClearAll}>
                {t("clearAll")}
              </Button>
            </div>
          </div>
        </div>

        {/* Keyword Group List */}
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {filteredKeywordGroups.map((group) => (
            <div
              key={group.id}
              className={`border rounded-lg p-3 transition-colors ${
                selectedKeywordGroups.includes(group.id)
                  ? "border-primary-300 bg-primary-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
            >
              <div className="flex items-start space-x-3">
                <Checkbox
                  checked={selectedKeywordGroups.includes(group.id)}
                  onCheckedChange={(checked) =>
                    handleKeywordGroupToggle(group.id, checked as boolean)
                  }
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-3 h-3 rounded-full ${group.color}`}
                      ></div>
                      <h4 className="font-medium text-sm">{group.name}</h4>
                    </div>
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Users className="w-3 h-3" />
                      <span>{group.assignees.join(", ")}</span>
                    </div>
                  </div>

                  <div className="mt-2 flex flex-wrap gap-1">
                    {group.keywords.slice(0, 5).map((keyword, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {keyword}
                      </Badge>
                    ))}
                    {group.keywords.length > 5 && (
                      <Badge variant="outline" className="text-xs">
                        +{group.keywords.length - 5}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredKeywordGroups.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            <Target className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">{t("noResults")}</p>
          </div>
        )}

        {/* Selected Summary */}
        {selectedKeywordGroups.length > 0 && (
          <div className="border-t pt-3">
            <div className="text-xs text-gray-600 mb-2">
              {t("selectedSummary")}
            </div>
            <div className="text-xs text-gray-800">
              {t("totalKeywords")}{" "}
              {selectedKeywordGroups.reduce((total, groupId) => {
                const group = keywordGroups.find((g) => g.id === groupId);
                return total + (group?.keywords.length || 0);
              }, 0)}{" "}
              {t("keywordsUnit")}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
