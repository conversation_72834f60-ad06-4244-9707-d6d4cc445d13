export interface ChatHistory {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  hasCharts: boolean;
}

export interface AnalysisTheme {
  id: string;
  name: string;
  description: string;
  keywords: string[];
  dateRange: {
    start: string;
    end: string;
  };
  status: "active" | "paused" | "completed";
  dataCount: number;
}

export interface ReportConfig {
  title: string;
  description: string;
  timeRange: {
    start: string;
    end: string;
  };
  selectedChats: string[];
  selectedThemes: string[];
  includeCharts: boolean;
  includeTopPosts: boolean;
  customNotes: string;
}

export interface ReportHistory {
  id: string;
  title: string;
  description: string;
  createdAt: Date;
  type: "pdf" | "pptx";
  status: "draft" | "processing" | "completed";
  pages: number;
  charts: number;
  author: string;
  thumbnail?: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  config: Partial<ReportConfig>;
}

// Props interfaces for components
export interface ReportBasicInfoProps {
  readonly config: ReportConfig;
  readonly onConfigChange: (config: ReportConfig) => void;
}

export interface ChatSelectionProps {
  readonly chats: ChatHistory[];
  readonly selectedChats: string[];
  readonly searchTerm: string;
  readonly onSearchChange: (term: string) => void;
  readonly onSelectionChange: (chatId: string, checked: boolean) => void;
}

export interface ThemeSelectionProps {
  readonly themes: AnalysisTheme[];
  readonly selectedThemes: string[];
  readonly searchTerm: string;
  readonly onSelectionChange: (themeId: string, checked: boolean) => void;
}

export interface ReportOptionsProps {
  readonly config: ReportConfig;
  readonly onConfigChange: (config: ReportConfig) => void;
}

export interface ReportActionsProps {
  readonly config: ReportConfig;
  readonly isExporting: boolean;
  readonly onPreview: () => void;
  readonly onExport: (format: "pdf" | "pptx") => void;
  readonly onTemplateSelect: (template: Partial<ReportConfig>) => void;
}

export interface ReportHistoryFiltersProps {
  readonly searchTerm: string;
  readonly statusFilter: string;
  readonly onSearchChange: (term: string) => void;
  readonly onStatusFilterChange: (status: string) => void;
}

export interface ReportHistoryListProps {
  readonly reports: ReportHistory[];
  readonly currentPage: number;
  readonly totalPages: number;
  readonly reportsPerPage: number;
  readonly onPageChange: (page: number) => void;
}

export interface ReportCardProps {
  readonly report: ReportHistory;
  readonly onView?: (report: ReportHistory) => void;
  readonly onDownload?: (report: ReportHistory) => void;
  readonly onCopy?: (report: ReportHistory) => void;
  readonly onMore?: (report: ReportHistory) => void;
}

export interface TemplateCardProps {
  readonly template: ReportTemplate;
  readonly onSelect: (template: ReportTemplate) => void;
}

// Hook return types
export interface UseReportConfigReturn {
  config: ReportConfig;
  setConfig: (config: ReportConfig) => void;
  updateConfig: (updates: Partial<ReportConfig>) => void;
  resetConfig: () => void;
}

export interface UseReportHistoryReturn {
  filteredReports: ReportHistory[];
  currentReports: ReportHistory[];
  searchTerm: string;
  statusFilter: string;
  currentPage: number;
  totalPages: number;
  setSearchTerm: (term: string) => void;
  setStatusFilter: (status: string) => void;
  setCurrentPage: (page: number) => void;
}

export interface UseReportExportReturn {
  isExporting: boolean;
  exportReport: (format: "pdf" | "pptx") => Promise<void>;
}
