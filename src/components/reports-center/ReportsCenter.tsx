import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { FileText } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { ReportPreview } from "../report-preview";
import {
  CreateReportTab,
  ReportHistoryTab,
  ReportTemplatesTab,
} from "./components";
import { sampleChats, sampleReportHistory, sampleThemes } from "./constants";
import { useReportConfig, useReportExport } from "./hooks";

export function ReportsCenter() {
  const { t } = useTranslation("reportsCenter");
  const [activeTab, setActiveTab] = useState("create");
  const [showPreview, setShowPreview] = useState(false);

  const { config, updateConfig } = useReportConfig();
  const { isExporting, exportReport } = useReportExport();

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handleExport = async (format: "pdf" | "pptx") => {
    await exportReport(format);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleTemplateSelect = (templateConfig: any) => {
    updateConfig(templateConfig);
    setActiveTab("create");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-400 to-secondary-400 text-white rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-2">
          <FileText className="w-6 h-6" />
          <h1 className="text-2xl font-bold">{t("title")}</h1>
        </div>
        <p className="text-white/90">{t("subtitle")}</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="create">{t("createReport")}</TabsTrigger>
          <TabsTrigger value="history">{t("reportHistory")}</TabsTrigger>
          <TabsTrigger value="templates">{t("reportTemplates")}</TabsTrigger>
        </TabsList>

        {/* Create Report Tab */}
        <TabsContent value="create" className="space-y-6">
          <CreateReportTab
            config={config}
            chats={sampleChats}
            themes={sampleThemes}
            isExporting={isExporting}
            onConfigChange={updateConfig}
            onPreview={handlePreview}
            onExport={handleExport}
          />
        </TabsContent>

        {/* Report History Tab */}
        <TabsContent value="history" className="space-y-6">
          <ReportHistoryTab reports={sampleReportHistory} />
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <ReportTemplatesTab onTemplateSelect={handleTemplateSelect} />
        </TabsContent>
      </Tabs>

      {/* Report Preview Modal */}
      {showPreview && (
        <ReportPreview
          config={config}
          chats={sampleChats.filter((chat) =>
            config.selectedChats.includes(chat.id),
          )}
          themes={sampleThemes.filter((theme) =>
            config.selectedThemes.includes(theme.id),
          )}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
}
