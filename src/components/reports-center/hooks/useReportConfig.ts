import { useCallback, useState } from "react";
import { DEFAULT_REPORT_CONFIG } from "../constants";
import type { ReportConfig, UseReportConfigReturn } from "../types";

export function useReportConfig(
  initialConfig?: Partial<ReportConfig>,
): UseReportConfigReturn {
  const [config, setConfig] = useState<ReportConfig>({
    ...DEFAULT_REPORT_CONFIG,
    ...initialConfig,
  });

  const updateConfig = useCallback((updates: Partial<ReportConfig>) => {
    setConfig((prev) => ({ ...prev, ...updates }));
  }, []);

  const resetConfig = useCallback(() => {
    setConfig(DEFAULT_REPORT_CONFIG);
  }, []);

  return {
    config,
    setConfig,
    updateConfig,
    resetConfig,
  };
}
