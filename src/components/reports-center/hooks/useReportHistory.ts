import { useMemo, useState } from "react";
import { REPORTS_PER_PAGE } from "../constants";
import type { ReportHistory, UseReportHistoryReturn } from "../types";

export function useReportHistory(
  reports: ReportHistory[],
): UseReportHistoryReturn {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);

  // Filter reports based on search term and status
  const filteredReports = useMemo(() => {
    return reports.filter((report) => {
      const matchesSearch = report.title
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesStatus =
        statusFilter === "all" || report.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [reports, searchTerm, statusFilter]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredReports.length / REPORTS_PER_PAGE);
  const indexOfLastReport = currentPage * REPORTS_PER_PAGE;
  const indexOfFirstReport = indexOfLastReport - REPORTS_PER_PAGE;
  const currentReports = filteredReports.slice(
    indexOfFirstReport,
    indexOfLastReport,
  );

  // Reset to first page when filters change
  const handleSearchChange = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return {
    filteredReports,
    currentReports,
    searchTerm,
    statusFilter,
    currentPage,
    totalPages,
    setSearchTerm: handleSearchChange,
    setStatusFilter: handleStatusFilterChange,
    setCurrentPage: handlePageChange,
  };
}
