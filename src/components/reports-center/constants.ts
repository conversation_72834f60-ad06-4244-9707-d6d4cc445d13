import type {
  AnalysisTheme,
  ChatHistory,
  ReportHistory,
  ReportTemplate,
} from "./types";

export const sampleChats: ChatHistory[] = [
  {
    id: "1",
    title: "品牌聲量分析",
    lastMessage: "過去一週品牌討論量增加了15%",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    messageCount: 8,
    hasCharts: true,
  },
  {
    id: "2",
    title: "產品評價分析",
    lastMessage: "主要抱怨集中在配送速度問題",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    messageCount: 6,
    hasCharts: true,
  },
  {
    id: "3",
    title: "競品監測",
    lastMessage: "競品推出新功能引發討論",
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    messageCount: 4,
    hasCharts: false,
  },
  {
    id: "4",
    title: "危機公關分析",
    lastMessage: "負面情緒已逐漸緩解",
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    messageCount: 12,
    hasCharts: true,
  },
];

export const sampleThemes: AnalysisTheme[] = [
  {
    id: "1",
    name: "品牌形象監測",
    description: "監測品牌在各平台的形象表現",
    keywords: ["品牌名", "產品", "服務", "評價"],
    dateRange: {
      start: "2025-01-01",
      end: "2025-01-21",
    },
    status: "active",
    dataCount: 2847,
  },
  {
    id: "2",
    name: "產品評價追蹤",
    description: "追蹤新產品上市後的用戶反饋",
    keywords: ["新產品", "功能", "使用體驗", "問題"],
    dateRange: {
      start: "2025-01-15",
      end: "2025-01-21",
    },
    status: "active",
    dataCount: 1456,
  },
  {
    id: "3",
    name: "競品分析",
    description: "監測主要競爭對手的市場動態",
    keywords: ["競品A", "競品B", "市場", "比較"],
    dateRange: {
      start: "2025-01-10",
      end: "2025-01-20",
    },
    status: "completed",
    dataCount: 892,
  },
  {
    id: "4",
    name: "危機監控",
    description: "即時監控可能的品牌危機事件",
    keywords: ["負面", "投訴", "問題", "危機"],
    dateRange: {
      start: "2025-01-01",
      end: "2025-01-21",
    },
    status: "paused",
    dataCount: 234,
  },
];

export const sampleReportHistory: ReportHistory[] = [
  {
    id: "report-001",
    title: "品牌聲量週報",
    description: "本週品牌聲量分析報告，包含社群媒體討論趨勢和情緒分析",
    createdAt: new Date(2025, 0, 20, 14, 30),
    type: "pdf",
    status: "completed",
    pages: 12,
    charts: 8,
    author: "王小明",
  },
  {
    id: "report-002",
    title: "產品評價分析報告",
    description: "新產品上市後用戶反饋分析，包含主要問題點和改進建議",
    createdAt: new Date(2025, 0, 18, 9, 15),
    type: "pptx",
    status: "processing",
    pages: 18,
    charts: 12,
    author: "李小華",
  },
  {
    id: "report-003",
    title: "競品監測月報",
    description: "主要競爭對手的市場動態分析，包含產品對比和市場份額變化",
    createdAt: new Date(2025, 0, 15, 16, 45),
    type: "pdf",
    status: "completed",
    pages: 24,
    charts: 15,
    author: "張小龍",
  },
  {
    id: "report-004",
    title: "社群媒體分析",
    description: "各社群平台表現分析，包含粉絲互動和內容效果評估",
    createdAt: new Date(2025, 0, 10, 11, 20),
    type: "pptx",
    status: "completed",
    pages: 16,
    charts: 10,
    author: "陳小美",
  },
  {
    id: "report-005",
    title: "危機事件追蹤報告",
    description: "產品召回事件的輿論追蹤，包含負面情緒分析和應對建議",
    createdAt: new Date(2025, 0, 5, 8, 30),
    type: "pdf",
    status: "draft",
    pages: 8,
    charts: 5,
    author: "林小豪",
  },
];

export const reportTemplates: ReportTemplate[] = [
  {
    id: "brand-voice",
    name: "品牌聲量報告",
    description: "包含聲量趨勢、來源分布、情緒分析等核心指標",
    icon: "TrendingUp",
    config: {
      title: "品牌聲量週報",
      description: "本週品牌聲量分析報告",
      includeCharts: true,
      includeTopPosts: true,
    },
  },
  {
    id: "crisis-monitoring",
    name: "危機監測報告",
    description: "專注於負面事件監測、影響評估和應對建議",
    icon: "AlertCircle",
    config: {
      title: "危機監測報告",
      description: "危機事件分析與應對建議",
      includeCharts: true,
      includeTopPosts: false,
    },
  },
  {
    id: "product-review",
    name: "產品評價報告",
    description: "深入分析產品評價、用戶反饋和改進建議",
    icon: "CheckCircle",
    config: {
      title: "產品評價分析",
      description: "產品上市後用戶反饋分析",
      includeCharts: true,
      includeTopPosts: true,
    },
  },
  {
    id: "competitor-analysis",
    name: "競品監測報告",
    description: "競爭對手市場動態分析",
    icon: "Target",
    config: {
      title: "競品監測報告",
      description: "競爭對手市場動態分析",
      includeCharts: true,
      includeTopPosts: false,
    },
  },
];

// Utility functions
export const getStatusColor = (status: string) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800";
    case "paused":
      return "bg-yellow-100 text-yellow-800";
    case "completed":
      return "bg-blue-100 text-blue-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const getReportStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "bg-green-100 text-green-800";
    case "processing":
      return "bg-blue-100 text-blue-800";
    case "draft":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const DEFAULT_REPORT_CONFIG = {
  title: "",
  description: "",
  timeRange: {
    start: "2025-01-01",
    end: "2025-01-21",
  },
  selectedChats: [],
  selectedThemes: [],
  includeCharts: true,
  includeTopPosts: true,
  customNotes: "",
};

export const REPORTS_PER_PAGE = 5;
