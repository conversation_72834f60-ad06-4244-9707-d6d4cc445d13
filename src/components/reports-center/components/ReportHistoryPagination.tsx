import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useTranslation } from "react-i18next";

interface ReportHistoryPaginationProps {
  readonly currentPage: number;
  readonly totalPages: number;
  readonly totalReports: number;
  readonly reportsPerPage: number;
  readonly onPageChange: (page: number) => void;
}

export function ReportHistoryPagination({
  currentPage,
  totalPages,
  totalReports,
  reportsPerPage,
  onPageChange,
}: ReportHistoryPaginationProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "history.pagination",
  });

  const indexOfFirstReport = (currentPage - 1) * reportsPerPage + 1;
  const indexOfLastReport = Math.min(
    currentPage * reportsPerPage,
    totalReports,
  );

  return (
    <div className="flex items-center justify-between mt-4">
      <div className="text-sm text-gray-500">
        {t("showing")} {indexOfFirstReport}
        {t("to")}
        {indexOfLastReport} {t("of")} {totalReports} {t("total")}
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div className="text-sm">
          {t("page")} {currentPage} {t("totalPages")} {totalPages} {t("pages")}
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
