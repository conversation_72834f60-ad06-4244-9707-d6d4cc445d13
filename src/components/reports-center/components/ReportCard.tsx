import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Calendar,
  Copy,
  Download,
  Eye,
  FileText,
  MoreHorizontal,
  Presentation,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { getReportStatusColor } from "../constants";
import type { ReportCardProps } from "../types";

export function ReportCard({
  report,
  onView,
  onDownload,
  onCopy,
  onMore,
}: ReportCardProps) {
  const { t } = useTranslation("reportsCenter");

  const getStatusText = (status: string) => {
    return t(`status.${status}`);
  };

  return (
    <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-4">
          <div className="bg-gray-100 p-3 rounded-lg">
            {report.type === "pdf" ? (
              <FileText className="w-8 h-8 text-primary" />
            ) : (
              <Presentation className="w-8 h-8 text-secondary" />
            )}
          </div>
          <div>
            <h3 className="font-medium text-lg">{report.title}</h3>
            <p className="text-sm text-gray-600 mt-1">{report.description}</p>
            <div className="flex flex-wrap items-center gap-2 mt-2">
              <Badge className={getReportStatusColor(report.status)}>
                {getStatusText(report.status)}
              </Badge>
              <Badge variant="outline">{report.type.toUpperCase()}</Badge>
              <div className="text-xs text-gray-500 flex items-center">
                <FileText className="w-3 h-3 mr-1" />
                {report.pages} {t("reportCard.pages")}
              </div>
              <div className="text-xs text-gray-500 flex items-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                {report.charts} {t("reportCard.charts")}
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <div className="flex space-x-2">
            {onView && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onView(report)}
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}
            {onDownload && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onDownload(report)}
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
            {onCopy && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onCopy(report)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            )}
            {onMore && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => onMore(report)}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            )}
          </div>
          <div className="text-xs text-gray-500 mt-2 flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            {report.createdAt.toLocaleDateString()}{" "}
            {report.createdAt.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {t("reportCard.createdBy")}: {report.author}
          </div>
        </div>
      </div>
    </div>
  );
}
