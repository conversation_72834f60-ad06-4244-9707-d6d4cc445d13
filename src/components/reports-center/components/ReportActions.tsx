import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Eye, FileText, Presentation } from "lucide-react";
import { useTranslation } from "react-i18next";
import { reportTemplates } from "../constants";
import type { ReportActionsProps } from "../types";

export function ReportActions({
  config,
  isExporting,
  onPreview,
  onExport,
  onTemplateSelect,
}: ReportActionsProps) {
  const { t } = useTranslation("reportsCenter.actions");
  const { t: templatesT } = useTranslation("reportsCenter.templates");

  const isDisabled =
    config.selectedChats.length === 0 && config.selectedThemes.length === 0;
  const isExportDisabled = isExporting || !config.title || isDisabled;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{t("preview")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            variant="outline"
            className="w-full"
            onClick={onPreview}
            disabled={isDisabled}
          >
            <Eye className="w-4 h-4 mr-2" />
            {t("preview")}
          </Button>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-3">{t("exportFormat")}</h4>
            <div className="space-y-2">
              <Button
                className="w-full"
                onClick={() => onExport("pdf")}
                disabled={isExportDisabled}
              >
                <FileText className="w-4 h-4 mr-2" />
                {isExporting ? t("exporting") : t("exportPdf")}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => onExport("pptx")}
                disabled={isExportDisabled}
              >
                <Presentation className="w-4 h-4 mr-2" />
                {isExporting ? t("exporting") : t("exportPptx")}
              </Button>
            </div>
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">{t("summary")}</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>
                {t("chatCount")}: {config.selectedChats.length}
              </div>
              <div>
                {t("themeCount")}: {config.selectedThemes.length}
              </div>
              <div>
                {t("timeRangeLabel")}: {config.timeRange.start} ~{" "}
                {config.timeRange.end}
              </div>
              <div>
                {t("includeChartsLabel")}:{" "}
                {config.includeCharts ? t("yes") : t("no")}
              </div>
              <div>
                {t("includePostsLabel")}:{" "}
                {config.includeTopPosts ? t("yes") : t("no")}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{templatesT("quickTemplates")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          {reportTemplates.map((template) => (
            <Button
              key={template.id}
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onTemplateSelect(template.config)}
            >
              {template.name}
            </Button>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
