import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Clock, MessageSquare, Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ChatSelectionProps } from "../types";

export function ChatSelection({
  chats,
  selectedChats,
  searchTerm,
  onSearchChange,
  onSelectionChange,
}: ChatSelectionProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "chatSelection",
  });

  const filteredChats = chats.filter((chat) =>
    chat.title.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="w-5 h-5" />
          <span>{t("title")}</span>
        </CardTitle>
        <Badge variant="secondary">
          {selectedChats.length} {t("selectedCount")}
        </Badge>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder={t("searchPlaceholder")}
              className="pl-10"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
        </div>
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {filteredChats.map((chat) => (
            <div
              key={chat.id}
              className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
            >
              <Checkbox
                checked={selectedChats.includes(chat.id)}
                onCheckedChange={(checked) =>
                  onSelectionChange(chat.id, checked as boolean)
                }
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{chat.title}</h4>
                  <div className="flex items-center space-x-2">
                    {chat.hasCharts && (
                      <Badge variant="outline">{t("hasCharts")}</Badge>
                    )}
                    <span className="text-xs text-gray-500">
                      {chat.messageCount} {t("messageCount")}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-1">{chat.lastMessage}</p>
                <div className="flex items-center mt-2 text-xs text-gray-500">
                  <Clock className="w-3 h-3 mr-1" />
                  {chat.timestamp.toLocaleString()}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
