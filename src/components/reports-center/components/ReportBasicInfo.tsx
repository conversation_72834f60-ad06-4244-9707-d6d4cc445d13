import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";
import type { ReportBasicInfoProps } from "../types";

export function ReportBasicInfo({
  config,
  onConfigChange,
}: ReportBasicInfoProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "basicInfo",
  });
  const { t: common } = useTranslation("common");

  const handleTitleChange = (value: string) => {
    onConfigChange({ ...config, title: value });
  };

  const handleDescriptionChange = (value: string) => {
    onConfigChange({ ...config, description: value });
  };

  const handleTimeRangeChange = (field: "start" | "end", value: string) => {
    onConfigChange({
      ...config,
      timeRange: {
        ...config.timeRange,
        [field]: value,
      },
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label
            htmlFor="report-title"
            className="text-sm font-medium mb-2 block"
          >
            {t("reportTitle")}
          </label>
          <Input
            id="report-title"
            placeholder={t("reportTitlePlaceholder")}
            value={config.title}
            onChange={(e) => handleTitleChange(e.target.value)}
          />
        </div>
        <div>
          <label
            htmlFor="report-description"
            className="text-sm font-medium mb-2 block"
          >
            {t("reportDescription")}
          </label>
          <Textarea
            id="report-description"
            placeholder={t("reportDescriptionPlaceholder")}
            value={config.description}
            onChange={(e) => handleDescriptionChange(e.target.value)}
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="start-date"
              className="text-sm font-medium mb-2 block"
            >
              {common("startDate")}
            </label>
            <Input
              id="start-date"
              type="date"
              value={config.timeRange.start}
              onChange={(e) => handleTimeRangeChange("start", e.target.value)}
            />
          </div>
          <div>
            <label
              htmlFor="end-date"
              className="text-sm font-medium mb-2 block"
            >
              {common("endDate")}
            </label>
            <Input
              id="end-date"
              type="date"
              value={config.timeRange.end}
              onChange={(e) => handleTimeRangeChange("end", e.target.value)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
