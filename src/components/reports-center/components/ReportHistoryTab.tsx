import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { useReportHistory } from "../hooks";
import type { ReportHistory } from "../types";
import { ReportHistoryFilters } from "./ReportHistoryFilters";
import { ReportHistoryList } from "./ReportHistoryList";
import { ReportHistoryPagination } from "./ReportHistoryPagination";

interface ReportHistoryTabProps {
  readonly reports: ReportHistory[];
}

export function ReportHistoryTab({ reports }: ReportHistoryTabProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "history",
  });

  const {
    filteredReports,
    currentReports,
    searchTerm,
    statusFilter,
    currentPage,
    totalPages,
    setSearchTerm,
    setStatusFilter,
    setCurrentPage,
  } = useReportHistory(reports);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>{t("title")}</CardTitle>
        <ReportHistoryFilters
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          onSearchChange={setSearchTerm}
          onStatusFilterChange={setStatusFilter}
        />
      </CardHeader>
      <CardContent>
        <ReportHistoryList
          reports={currentReports}
          currentPage={currentPage}
          totalPages={totalPages}
          reportsPerPage={5}
          onPageChange={setCurrentPage}
        />

        {filteredReports.length > 0 && (
          <ReportHistoryPagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalReports={filteredReports.length}
            reportsPerPage={5}
            onPageChange={setCurrentPage}
          />
        )}
      </CardContent>
    </Card>
  );
}
