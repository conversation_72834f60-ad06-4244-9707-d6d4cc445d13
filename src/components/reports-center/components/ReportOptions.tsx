import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";
import type { ReportOptionsProps } from "../types";

export function ReportOptions({ config, onConfigChange }: ReportOptionsProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "options",
  });

  const handleIncludeChartsChange = (checked: boolean) => {
    onConfigChange({ ...config, includeCharts: checked });
  };

  const handleIncludeTopPostsChange = (checked: boolean) => {
    onConfigChange({ ...config, includeTopPosts: checked });
  };

  const handleCustomNotesChange = (value: string) => {
    onConfigChange({ ...config, customNotes: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("title")}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={config.includeCharts}
            onCheckedChange={handleIncludeChartsChange}
          />
          <label className="text-sm font-medium">{t("includeCharts")}</label>
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={config.includeTopPosts}
            onCheckedChange={handleIncludeTopPostsChange}
          />
          <label className="text-sm font-medium">{t("includeTopPosts")}</label>
        </div>
        <div>
          <label className="text-sm font-medium mb-2 block">
            {t("customNotes")}
          </label>
          <Textarea
            placeholder={t("customNotesPlaceholder")}
            value={config.customNotes}
            onChange={(e) => handleCustomNotesChange(e.target.value)}
          />
        </div>
      </CardContent>
    </Card>
  );
}
