import { useState } from "react";
import type { AnalysisTheme, ChatHistory, ReportConfig } from "../types";
import { ChatSelection } from "./ChatSelection";
import { ReportActions } from "./ReportActions";
import { ReportBasicInfo } from "./ReportBasicInfo";
import { ReportOptions } from "./ReportOptions";
import { ThemeSelection } from "./ThemeSelection";

interface CreateReportTabProps {
  readonly config: ReportConfig;
  readonly chats: ChatHistory[];
  readonly themes: AnalysisTheme[];
  readonly isExporting: boolean;
  readonly onConfigChange: (config: ReportConfig) => void;
  readonly onPreview: () => void;
  readonly onExport: (format: "pdf" | "pptx") => void;
}

export function CreateReportTab({
  config,
  chats,
  themes,
  isExporting,
  onConfigChange,
  onPreview,
  onExport,
}: CreateReportTabProps) {
  const [searchTerm, setSearchTerm] = useState("");

  const handleChatSelection = (chatId: string, checked: boolean) => {
    const updatedChats = checked
      ? [...config.selectedChats, chatId]
      : config.selectedChats.filter((id) => id !== chatId);

    onConfigChange({ ...config, selectedChats: updatedChats });
  };

  const handleThemeSelection = (themeId: string, checked: boolean) => {
    const updatedThemes = checked
      ? [...config.selectedThemes, themeId]
      : config.selectedThemes.filter((id) => id !== themeId);

    onConfigChange({ ...config, selectedThemes: updatedThemes });
  };

  const handleTemplateSelect = (templateConfig: Partial<ReportConfig>) => {
    onConfigChange({ ...config, ...templateConfig });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Left Panel - Configuration */}
      <div className="lg:col-span-2 space-y-6">
        <ReportBasicInfo config={config} onConfigChange={onConfigChange} />

        <ChatSelection
          chats={chats}
          selectedChats={config.selectedChats}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onSelectionChange={handleChatSelection}
        />

        <ThemeSelection
          themes={themes}
          selectedThemes={config.selectedThemes}
          searchTerm={searchTerm}
          onSelectionChange={handleThemeSelection}
        />

        <ReportOptions config={config} onConfigChange={onConfigChange} />
      </div>

      {/* Right Panel - Actions */}
      <div className="space-y-6">
        <ReportActions
          config={config}
          isExporting={isExporting}
          onPreview={onPreview}
          onExport={onExport}
          onTemplateSelect={handleTemplateSelect}
        />
      </div>
    </div>
  );
}
