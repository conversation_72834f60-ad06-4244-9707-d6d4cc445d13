import { reportTemplates } from "../constants";
import type { ReportConfig, ReportTemplate } from "../types";
import { TemplateCard } from "./TemplateCard";

interface ReportTemplatesTabProps {
  readonly onTemplateSelect: (config: Partial<ReportConfig>) => void;
}

export function ReportTemplatesTab({
  onTemplateSelect,
}: ReportTemplatesTabProps) {
  const handleTemplateSelect = (template: ReportTemplate) => {
    onTemplateSelect(template.config);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {reportTemplates.map((template) => (
        <TemplateCard
          key={template.id}
          template={template}
          onSelect={handleTemplateSelect}
        />
      ))}
    </div>
  );
}
