import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Target } from "lucide-react";
import { useTranslation } from "react-i18next";
import { getStatusColor } from "../constants";
import type { ThemeSelectionProps } from "../types";

export function ThemeSelection({
  themes,
  selectedThemes,
  searchTerm,
  onSelectionChange,
}: ThemeSelectionProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "themeSelection",
  });
  const { t: statusT } = useTranslation("reportsCenter", {
    keyPrefix: "status",
  });

  const filteredThemes = themes.filter((theme) =>
    theme.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const getStatusText = (status: string) => {
    return statusT(status);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center space-x-2">
          <Target className="w-5 h-5" />
          <span>{t("title")}</span>
        </CardTitle>
        <Badge variant="secondary">
          {selectedThemes.length} {t("selectedCount")}
        </Badge>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {filteredThemes.map((theme) => (
            <div
              key={theme.id}
              className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50"
            >
              <Checkbox
                checked={selectedThemes.includes(theme.id)}
                onCheckedChange={(checked) =>
                  onSelectionChange(theme.id, checked as boolean)
                }
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{theme.name}</h4>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(theme.status)}>
                      {getStatusText(theme.status)}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {theme.dataCount.toLocaleString()} {t("dataCount")}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {theme.description}
                </p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {theme.keywords.slice(0, 3).map((keyword, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {keyword}
                    </Badge>
                  ))}
                  {theme.keywords.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{theme.keywords.length - 3} {t("moreKeywords")}
                    </Badge>
                  )}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {theme.dateRange.start} ~ {theme.dateRange.end}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
