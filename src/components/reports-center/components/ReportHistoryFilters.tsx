import { Input } from "@/components/ui/input";
import { Filter, Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ReportHistoryFiltersProps } from "../types";

export function ReportHistoryFilters({
  searchTerm,
  statusFilter,
  onSearchChange,
  onStatusFilterChange,
}: ReportHistoryFiltersProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "history",
  });
  const { t: statusT } = useTranslation("reportsCenter", {
    keyPrefix: "status",
  });

  return (
    <div className="flex items-center space-x-2">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
        <Input
          placeholder={t("searchPlaceholder")}
          className="pl-10 w-64"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
      <div className="relative">
        <select
          className="pl-8 pr-4 py-2 border rounded-md w-32 appearance-none"
          value={statusFilter}
          onChange={(e) => onStatusFilterChange(e.target.value)}
        >
          <option value="all">{t("allStatuses")}</option>
          <option value="completed">{statusT("completed")}</option>
          <option value="processing">{statusT("processing")}</option>
          <option value="draft">{statusT("draft")}</option>
        </select>
        <Filter className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
      </div>
    </div>
  );
}
