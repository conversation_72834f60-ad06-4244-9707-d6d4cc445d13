import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, CheckCircle, Target, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { TemplateCardProps } from "../types";

const iconMap = {
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Target,
};

export function TemplateCard({ template, onSelect }: TemplateCardProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "templates",
  });

  const IconComponent =
    iconMap[template.icon as keyof typeof iconMap] || TrendingUp;

  const getColorClass = (iconName: string) => {
    switch (iconName) {
      case "TrendingUp":
        return "bg-primary-100 text-primary";
      case "AlertCircle":
        return "bg-secondary-100 text-secondary";
      case "CheckCircle":
        return "bg-green-100 text-green-600";
      case "Target":
        return "bg-blue-100 text-blue-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardContent className="p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div
            className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClass(template.icon)}`}
          >
            <IconComponent className="w-6 h-6" />
          </div>
          <div>
            <h3 className="font-semibold">{template.name}</h3>
            <p className="text-sm text-gray-600">{template.description}</p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => onSelect(template)}
        >
          {t("useTemplate")}
        </Button>
      </CardContent>
    </Card>
  );
}
