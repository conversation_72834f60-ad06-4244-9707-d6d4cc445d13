/* eslint-disable @typescript-eslint/no-explicit-any */
import { FileText } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ReportHistoryListProps } from "../types";
import { ReportCard } from "./ReportCard";

export function ReportHistoryList({ reports }: ReportHistoryListProps) {
  const { t } = useTranslation("reportsCenter", {
    keyPrefix: "messages",
  });

  const handleReportView = (report: any) => {
    // Handle report view action
    console.log("View report:", report);
  };

  const handleReportDownload = (report: any) => {
    // Handle report download action
    console.log("Download report:", report);
  };

  const handleReportCopy = (report: any) => {
    // Handle report copy action
    console.log("Copy report:", report);
  };

  const handleReportMore = (report: any) => {
    // Handle report more actions
    console.log("More actions for report:", report);
  };

  if (reports.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>{t("noReportsFound")}</p>
        <p className="text-sm">{t("adjustFilters")}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {reports.map((report) => (
        <ReportCard
          key={report.id}
          report={report}
          onView={handleReportView}
          onDownload={handleReportDownload}
          onCopy={handleReportCopy}
          onMore={handleReportMore}
        />
      ))}
    </div>
  );
}
