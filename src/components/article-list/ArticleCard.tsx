import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Clock,
  ExternalLink,
  Eye,
  Heart,
  MessageSquare,
  Share,
  User,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ArticleCardProps } from "./types";
import { getMediaTypeIcon, getSentimentColor } from "./utils";

export function ArticleCard({
  article,
  isSelected,
  onSelect,
  index,
}: Readonly<ArticleCardProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "article",
  });
  const { t: common } = useTranslation("common");

  const getSentimentText = (sentiment: string) => {
    switch (sentiment) {
      case "positive":
        return common("positive");
      case "negative":
        return common("negative");
      default:
        return common("neutral");
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <Checkbox
            checked={isSelected}
            onCheckedChange={(checked) =>
              onSelect(article.id, checked as boolean)
            }
          />

          <div className="text-sm font-medium text-gray-500 mt-1">{index}.</div>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <h3 className="text-base font-medium text-blue-600 hover:text-blue-800 cursor-pointer">
                {article.title}
              </h3>
              <Button variant="ghost" size="sm">
                <ExternalLink className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex items-center space-x-3 mb-2 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{article.publishedAt.toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <span>{article.platformIcon}</span>
                <span>{article.platform}</span>
                {/* 顯示板塊資訊 */}
                {article.board && (
                  <>
                    <span className="text-gray-400">·</span>
                    <Badge variant="outline" className="text-xs">
                      {article.board}
                    </Badge>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-1">
                <User className="w-4 h-4" />
                <span>{article.author}</span>
                {article.verified && (
                  <Badge className="bg-blue-100 text-blue-800 text-xs">
                    {t("verified")}
                  </Badge>
                )}
              </div>
            </div>

            <p className="text-sm text-gray-700 mb-3">{article.content}</p>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{article.metrics.views.toLocaleString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Heart className="w-4 h-4" />
                  <span>{article.metrics.likes.toLocaleString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MessageSquare className="w-4 h-4" />
                  <span>{article.metrics.comments}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Share className="w-4 h-4" />
                  <span>{article.metrics.shares}</span>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Badge
                  className={`text-xs ${getSentimentColor(article.sentiment)}`}
                >
                  {getSentimentText(article.sentiment)}
                </Badge>
                {getMediaTypeIcon(article.mediaType)}
              </div>
            </div>

            <div className="flex flex-wrap gap-1 mt-2">
              {article.tags.map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
