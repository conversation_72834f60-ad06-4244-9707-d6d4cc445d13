import { FileText } from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { ArticleCard } from "./ArticleCard";
import { ArticleListHeader } from "./ArticleListHeader";
import {
  availableClusters,
  categoryOptions,
  sentimentOptions,
} from "./constants";
import { FilterPanel } from "./FilterPanel";
import { Pagination } from "./Pagination";
import { SearchBar } from "./SearchBar";
import { SelectionControls } from "./SelectionControls";
import { SortControls } from "./SortControls";
import type { ArticleItem, FilterState, SortBy, SortOrder } from "./types";
import { generateSampleArticles } from "./utils";

const sampleArticles = generateSampleArticles();

export function ArticleList() {
  const { t } = useTranslation("articleList");
  const [articles] = useState<ArticleItem[]>(sampleArticles);
  const [filteredArticles, setFilteredArticles] =
    useState<ArticleItem[]>(sampleArticles);
  const [filters, setFilters] = useState<FilterState>({
    keywords: "",
    clusters: [],
    sources: [],
    boards: [],
    sentiments: [],
    categories: [],
    dateRange: undefined,
  });

  const [sortBy, setSortBy] = useState<SortBy>("publishedAt");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);
  const [expandedClusters, setExpandedClusters] = useState<string[]>([]);
  const [expandedSources, setExpandedSources] = useState<string[]>([]);

  const itemsPerPage = 10;

  // 過濾和排序邏輯
  useEffect(() => {
    let filtered = [...articles];

    if (filters.keywords) {
      filtered = filtered.filter(
        (article) =>
          article.title
            .toLowerCase()
            .includes(filters.keywords.toLowerCase()) ||
          article.content
            .toLowerCase()
            .includes(filters.keywords.toLowerCase()) ||
          article.author
            .toLowerCase()
            .includes(filters.keywords.toLowerCase()) ||
          article.board?.toLowerCase().includes(filters.keywords.toLowerCase()),
      );
    }

    if (filters.clusters.length > 0) {
      filtered = filtered.filter(
        (article) =>
          article.clusterId && filters.clusters.includes(article.clusterId),
      );
    }

    if (filters.sources.length > 0) {
      filtered = filtered.filter(
        (article) =>
          article.sourceId && filters.sources.includes(article.sourceId),
      );
    }

    if (filters.boards.length > 0) {
      filtered = filtered.filter(
        (article) =>
          article.boardId && filters.boards.includes(article.boardId),
      );
    }

    if (filters.sentiments.length > 0) {
      filtered = filtered.filter((article) =>
        filters.sentiments.includes(article.sentiment),
      );
    }

    if (filters.categories.length > 0) {
      filtered = filtered.filter((article) =>
        filters.categories.includes(article.category),
      );
    }

    if (filters.dateRange?.from && filters.dateRange?.to) {
      filtered = filtered.filter((article) => {
        const publishedDate = new Date(article.publishedAt);
        return (
          publishedDate >= filters.dateRange!.from! &&
          publishedDate <= filters.dateRange!.to!
        );
      });
    }

    filtered.sort((a, b) => {
      let aValue, bValue;
      switch (sortBy) {
        case "views":
          aValue = a.metrics.views;
          bValue = b.metrics.views;
          break;
        case "likes":
          aValue = a.metrics.likes;
          bValue = b.metrics.likes;
          break;
        default:
          aValue = a.publishedAt.getTime();
          bValue = b.publishedAt.getTime();
      }
      return sortOrder === "desc" ? bValue - aValue : aValue - bValue;
    });

    setFilteredArticles(filtered);
    setCurrentPage(1);
  }, [articles, filters, sortBy, sortOrder]);

  const totalPages = Math.ceil(filteredArticles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentArticles = filteredArticles.slice(startIndex, endIndex);

  const handleFilterChange = (
    key: keyof FilterState,
    value: FilterState[keyof FilterState],
  ) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleArrayFilterChange = (
    key: keyof FilterState,
    value: string,
    checked: boolean,
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: checked
        ? [...(prev[key] as string[]), value]
        : (prev[key] as string[]).filter((item) => item !== value),
    }));
  };

  const handleSelectArticle = (articleId: string, checked: boolean) => {
    setSelectedArticles((prev) =>
      checked ? [...prev, articleId] : prev.filter((id) => id !== articleId),
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectedArticles(checked ? currentArticles.map((a) => a.id) : []);
  };

  const toggleClusterExpansion = (clusterId: string) => {
    setExpandedClusters((prev) =>
      prev.includes(clusterId)
        ? prev.filter((id) => id !== clusterId)
        : [...prev, clusterId],
    );
  };

  const toggleSourceExpansion = (sourceId: string) => {
    setExpandedSources((prev) =>
      prev.includes(sourceId)
        ? prev.filter((id) => id !== sourceId)
        : [...prev, sourceId],
    );
  };

  return (
    <div className="w-full h-full bg-white flex flex-col">
      {/* 固定頂部區域 */}
      <div className="border-b bg-white">
        {/* Header */}
        <ArticleListHeader
          onToggleFilters={() => setShowFilters(!showFilters)}
          showFilters={showFilters}
        />

        {/* Search Bar */}
        <div className="px-6 pb-4">
          <SearchBar
            value={filters.keywords}
            onChange={(value) => handleFilterChange("keywords", value)}
          />
        </div>

        {/* Filters Panel */}
        <FilterPanel
          filters={filters}
          onFilterChange={handleFilterChange}
          onArrayFilterChange={handleArrayFilterChange}
          isVisible={showFilters}
          availableClusters={availableClusters}
          sentimentOptions={sentimentOptions}
          categoryOptions={categoryOptions}
          expandedClusters={expandedClusters}
          expandedSources={expandedSources}
          onToggleClusterExpansion={toggleClusterExpansion}
          onToggleSourceExpansion={toggleSourceExpansion}
        />

        {/* Controls */}
        <div className="px-6 py-4 bg-white border-b">
          <div className="flex items-center justify-between">
            <SelectionControls
              selectedCount={selectedArticles.length}
              totalCount={currentArticles.length}
              onSelectAll={handleSelectAll}
              filterStats={{
                clustersCount: filters.clusters.length,
                sourcesCount: filters.sources.length,
                boardsCount: filters.boards.length,
              }}
            />

            <div className="flex items-center space-x-4">
              <SortControls
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortByChange={setSortBy}
                onSortOrderChange={setSortOrder}
              />
              <span className="text-sm text-gray-600">
                共 {filteredArticles.length} 筆
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 可滾動的文章列表區域 */}
      <div className="flex-1 overflow-y-auto p-6">
        {currentArticles.length > 0 ? (
          <div className="space-y-4">
            {currentArticles.map((article, index) => (
              <ArticleCard
                key={article.id}
                article={article}
                isSelected={selectedArticles.includes(article.id)}
                onSelect={handleSelectArticle}
                index={startIndex + index + 1}
              />
            ))}

            {/* 分頁 */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={filteredArticles.length}
              itemsPerPage={itemsPerPage}
              startIndex={startIndex}
              endIndex={endIndex}
              onPageChange={setCurrentPage}
            />
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg">{t("noArticles")}</p>
          </div>
        )}
      </div>
    </div>
  );
}
