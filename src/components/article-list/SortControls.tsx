import { useTranslation } from "react-i18next";
import type { SortBy, SortControlsProps } from "./types";

export function SortControls({
  sortBy,
  onSortByChange,
}: Readonly<SortControlsProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "controls",
  });

  return (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-gray-600">{t("sortBy")}:</span>
      <select
        className="text-sm border rounded px-2 py-1"
        value={sortBy}
        onChange={(e) => onSortByChange(e.target.value as SortBy)}
      >
        <option value="publishedAt">{t("sortByDate")}</option>
        <option value="views">{t("sortByViews")}</option>
        <option value="likes">{t("sortByLikes")}</option>
      </select>
    </div>
  );
}
