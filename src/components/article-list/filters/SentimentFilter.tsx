import { Badge } from "@/components/ui/badge";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslation } from "react-i18next";
import type { SentimentFilterProps } from "../types";

export function SentimentFilter({
  sentimentOptions,
  selectedSentiments,
  onFilterChange,
}: Readonly<SentimentFilterProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "filters",
  });

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">{t("sentiment")}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {sentimentOptions.map((sentiment) => (
            <div key={sentiment.value} className="flex items-center space-x-2">
              <Checkbox
                checked={selectedSentiments.includes(sentiment.value)}
                onCheckedChange={(checked) =>
                  onFilterChange(
                    "sentiments",
                    sentiment.value,
                    checked as boolean,
                  )
                }
              />
              <Badge className={`${sentiment.color} text-white text-xs`}>
                {sentiment.label}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
