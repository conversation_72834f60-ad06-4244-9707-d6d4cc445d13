import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { DateRangePicker } from "../../ui/date-range-picker";
import type { DateRangeFilterProps } from "../types";

export function DateRangeFilter({
  dateRange,
  onDateRangeChange,
}: Readonly<DateRangeFilterProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "filters",
  });

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">{t("dateRange")}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <DateRangePicker value={dateRange} onChange={onDateRangeChange} />
      </CardContent>
    </Card>
  );
}
