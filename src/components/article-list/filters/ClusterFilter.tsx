import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ClusterFilterProps } from "../types";

export function ClusterFilter({
  availableClusters,
  selectedClusters,
  selectedSources,
  selectedBoards,
  onFilterChange,
  expandedClusters,
  expandedSources,
  onToggleClusterExpansion,
  onToggleSourceExpansion,
}: Readonly<ClusterFilterProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "filters",
  });

  return (
    <Card className="lg:col-span-2">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center justify-between">
          {t("clusterFilter")}
          <Badge variant="outline" className="text-xs">
            {selectedClusters.length} {t("clusters")} · {selectedSources.length}{" "}
            {t("sources")} · {selectedBoards.length} {t("boards")}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 max-h-80 overflow-y-auto">
          {availableClusters.map((cluster) => (
            <div key={cluster.id} className="border rounded p-2">
              <div
                className="flex items-center justify-between cursor-pointer"
                onClick={() => onToggleClusterExpansion(cluster.id)}
              >
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={selectedClusters.includes(cluster.id)}
                    onCheckedChange={(checked) =>
                      onFilterChange("clusters", cluster.id, checked as boolean)
                    }
                    onClick={(e) => e.stopPropagation()}
                  />
                  <span className="text-sm font-medium">{cluster.name}</span>
                </div>
                <ChevronDown
                  className={`w-4 h-4 transition-transform ${
                    expandedClusters.includes(cluster.id) ? "rotate-180" : ""
                  }`}
                />
              </div>

              {expandedClusters.includes(cluster.id) && (
                <div className="mt-2 space-y-1 pl-4 border-l-2 border-gray-200">
                  {cluster.sources.map((source) => (
                    <div
                      key={source.id}
                      className="border rounded p-2 bg-white"
                    >
                      <div
                        className="flex items-center justify-between cursor-pointer"
                        onClick={() => onToggleSourceExpansion(source.id)}
                      >
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            checked={selectedSources.includes(source.id)}
                            onCheckedChange={(checked) =>
                              onFilterChange(
                                "sources",
                                source.id,
                                checked as boolean,
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                          <span className="text-sm font-medium">
                            {source.name}
                          </span>
                          {source.boards && source.boards.length > 0 && (
                            <Badge variant="outline" className="text-xs">
                              {
                                source.boards.filter((board) =>
                                  selectedBoards.includes(board.id),
                                ).length
                              }
                              /{source.boards.length}
                            </Badge>
                          )}
                        </div>
                        {source.boards && source.boards.length > 0 && (
                          <ChevronRight
                            className={`w-3 h-3 transition-transform ${
                              expandedSources.includes(source.id)
                                ? "rotate-90"
                                : ""
                            }`}
                          />
                        )}
                      </div>

                      {expandedSources.includes(source.id) && source.boards && (
                        <div className="mt-2 space-y-1 pl-4 border-l border-gray-200">
                          {source.boards.map((board) => (
                            <div
                              key={board.id}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                checked={selectedBoards.includes(board.id)}
                                onCheckedChange={(checked) =>
                                  onFilterChange(
                                    "boards",
                                    board.id,
                                    checked as boolean,
                                  )
                                }
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <span className="text-xs font-medium truncate">
                                    {board.name}
                                  </span>
                                  {board.memberCount && (
                                    <span className="text-xs text-gray-500">
                                      {(board.memberCount / 1000).toFixed(0)}k
                                    </span>
                                  )}
                                </div>
                                <p className="text-xs text-gray-500 truncate">
                                  {board.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
