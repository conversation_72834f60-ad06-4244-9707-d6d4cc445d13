import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslation } from "react-i18next";
import type { CategoryFilterProps } from "../types";

export function CategoryFilter({
  categoryOptions,
  selectedCategories,
  onFilterChange,
}: Readonly<CategoryFilterProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "filters",
  });

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm">{t("categories")}</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {categoryOptions.map((category) => (
            <div key={category.value} className="flex items-center space-x-2">
              <Checkbox
                checked={selectedCategories.includes(category.value)}
                onCheckedChange={(checked) =>
                  onFilterChange(
                    "categories",
                    category.value,
                    checked as boolean,
                  )
                }
              />
              <span className="text-sm">{category.label}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
