import type { DateRange } from "react-day-picker";

export interface ArticleItem {
  id: string;
  title: string;
  content: string;
  author: string;
  platform: string;
  platformIcon: string;
  board?: string;
  boardId?: string;
  clusterId?: string;
  sourceId?: string;
  publishedAt: Date;
  metrics: {
    views: number;
    likes: number;
    shares: number;
    comments: number;
  };
  sentiment: "positive" | "negative" | "neutral";
  category: string;
  tags: string[];
  url: string;
  mediaType: "text" | "image" | "video";
  verified: boolean;
}

export interface FilterState {
  keywords: string;
  clusters: string[];
  sources: string[];
  boards: string[];
  sentiments: string[];
  categories: string[];
  dateRange: DateRange | undefined;
}

export interface BoardItem {
  id: string;
  name: string;
  description: string;
  memberCount?: number;
  postCount?: number;
}

export interface SourceItem {
  id: string;
  name: string;
  description: string;
  boards?: BoardItem[];
  memberCount?: number;
}

export interface Cluster {
  id: string;
  name: string;
  description: string;
  sourceCount: number;
  excludeCount: number;
  totalCount: number;
  status: "active" | "paused" | "inactive";
  sources: SourceItem[];
}

export interface SentimentOption {
  value: "positive" | "negative" | "neutral";
  label: string;
  color: string;
}

export interface CategoryOption {
  value: string;
  label: string;
}

export type SortBy = "publishedAt" | "views" | "likes";
export type SortOrder = "asc" | "desc";
export type ViewMode = "list" | "grid";

// Component Props Types
export interface ArticleCardProps {
  article: ArticleItem;
  isSelected: boolean;
  onSelect: (articleId: string, checked: boolean) => void;
  index: number;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export interface FilterPanelProps {
  filters: FilterState;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onFilterChange: (key: keyof FilterState, value: any) => void;
  onArrayFilterChange: (
    key: keyof FilterState,
    value: string,
    checked: boolean,
  ) => void;
  isVisible: boolean;
  availableClusters: Cluster[];
  sentimentOptions: SentimentOption[];
  categoryOptions: CategoryOption[];
  expandedClusters: string[];
  expandedSources: string[];
  onToggleClusterExpansion: (clusterId: string) => void;
  onToggleSourceExpansion: (sourceId: string) => void;
}

export interface ClusterFilterProps {
  availableClusters: Cluster[];
  selectedClusters: string[];
  selectedSources: string[];
  selectedBoards: string[];
  onFilterChange: (
    key: keyof FilterState,
    value: string,
    checked: boolean,
  ) => void;
  expandedClusters: string[];
  expandedSources: string[];
  onToggleClusterExpansion: (clusterId: string) => void;
  onToggleSourceExpansion: (sourceId: string) => void;
}

export interface SentimentFilterProps {
  sentimentOptions: SentimentOption[];
  selectedSentiments: string[];
  onFilterChange: (
    key: keyof FilterState,
    value: string,
    checked: boolean,
  ) => void;
}

export interface DateRangeFilterProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (dateRange: DateRange | undefined) => void;
}

export interface CategoryFilterProps {
  categoryOptions: CategoryOption[];
  selectedCategories: string[];
  onFilterChange: (
    key: keyof FilterState,
    value: string,
    checked: boolean,
  ) => void;
}

export interface SortControlsProps {
  sortBy: SortBy;
  sortOrder: SortOrder;
  onSortByChange: (sortBy: SortBy) => void;
  onSortOrderChange: (sortOrder: SortOrder) => void;
}

export interface SelectionControlsProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: (checked: boolean) => void;
  filterStats: {
    clustersCount: number;
    sourcesCount: number;
    boardsCount: number;
  };
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  startIndex: number;
  endIndex: number;
  onPageChange: (page: number) => void;
}

export interface ArticleListHeaderProps {
  onToggleFilters: () => void;
  showFilters: boolean;
}
