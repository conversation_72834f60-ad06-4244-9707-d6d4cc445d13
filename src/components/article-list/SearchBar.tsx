import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { SearchBarProps } from "./types";

export function SearchBar({
  value,
  onChange,
  placeholder,
}: Readonly<SearchBarProps>) {
  const { t } = useTranslation("articleList");

  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
      <Input
        placeholder={placeholder || t("searchPlaceholder")}
        className="pl-12 h-12"
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  );
}
