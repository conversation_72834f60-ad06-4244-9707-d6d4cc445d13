import React from "react";
import { FileText, Video, ImageIcon } from "lucide-react";
import type { ArticleItem } from "./types";
import { availableClusters } from "./constants";

// 獲取來源圖示
export const getSourceIcon = (sourceName: string): string => {
  const iconMap: { [key: string]: string } = {
    聯合新聞網: "📰",
    自由時報: "📰",
    中時新聞網: "📰",
    ETtoday: "📰",
    TVBS新聞: "📺",
    Facebook: "📘",
    Instagram: "📷",
    Twitter: "🐦",
    TikTok: "🎵",
    YouTube: "🎥",
    PTT: "💬",
    Dcard: "🎓",
    Mobile01: "📱",
    巴哈姆特: "🎮",
    Google評論: "⭐",
    "App Store": "📱",
    "Google Play": "🤖",
    Tripadvisor: "✈️",
    痞客邦: "📝",
    Medium: "📖",
    個人部落格: "📝",
    專業媒體: "📰",
  };
  return iconMap[sourceName] || "📄";
};

export const getSentimentColor = (sentiment: string) => {
  switch (sentiment) {
    case "positive":
      return "text-green-600 bg-green-50";
    case "negative":
      return "text-red-600 bg-red-50";
    default:
      return "text-gray-600 bg-gray-50";
  }
};

export const getMediaTypeIcon = (mediaType: string): React.ReactElement => {
  switch (mediaType) {
    case "video":
      return <Video className="w-4 h-4" />;
    case "image":
      return <ImageIcon className="w-4 h-4" />;
    default:
      return <FileText className="w-4 h-4" />;
  }
};

// 生成測試文章資料，包含群集、來源、板塊資訊
export const generateSampleArticles = (): ArticleItem[] => {
  const baseArticles: Partial<ArticleItem>[] = [
    {
      id: "1",
      title:
        "【完整版中字】英雄：道345會出現社中體帶版　增10：第20出些錯來界廊？20250617｜#林系翼君虎經社中資誠編論學文系體景第元之修樓活圖",
      content: "道然期不暖幸了！完沒會實想迴實！😍😍😍 暖淋也不環了！",
      author: "@kevin03",
      publishedAt: new Date("2025-06-19 11:04:14"),
      metrics: { views: 11600, likes: 503, shares: 26, comments: 89 },
      sentiment: "positive",
      category: "娛樂",
      tags: ["政治", "新聞"],
      url: "https://youtube.com/watch?v=123",
      mediaType: "video",
      verified: false,
      clusterId: "2",
      sourceId: "youtube",
      platform: "YouTube",
      platformIcon: "🎥",
    },
    {
      id: "2",
      title:
        "今天我和違處安市長、陳錫華議員、台北市議會李伯蘇，議市議會員地方…",
      content: "全球不信贏樂約告！",
      author: "陳其邁",
      publishedAt: new Date("2025-06-19 09:07:32"),
      metrics: { views: 12700, likes: 445, shares: 34, comments: 127 },
      sentiment: "neutral",
      category: "政治",
      tags: ["市政", "政治人物"],
      url: "https://facebook.com/post/456",
      mediaType: "text",
      verified: true,
      clusterId: "2",
      sourceId: "facebook",
      platform: "Facebook",
      platformIcon: "📘",
    },
  ];

  // 生成更多測試文章
  const additionalArticles = Array.from({ length: 30 }, (_, i) => {
    const cluster = availableClusters[i % availableClusters.length];
    const source = cluster.sources[i % cluster.sources.length];
    const board = source.boards?.[i % (source.boards?.length || 1)];

    return {
      id: `${i + 3}`,
      title: `測試文章標題 ${i + 3} - 這是一個用來測試滾動功能的長標題，包含各種關鍵字和內容，確保有足夠的內容可以滾動查看`,
      content: `這是第 ${i + 3} 篇測試文章的內容。內容包含了各種社群媒體的討論話題，用來測試系統的搜尋和篩選功能。這裡有更多的文字內容來確保每個文章卡片都有足夠的高度，讓滾動功能更明顯。`,
      author: `測試用戶${i + 3}`,
      publishedAt: new Date(2025, 5, 19 - i, 10, 0, 0),
      metrics: {
        views: Math.floor(Math.random() * 50000) + 1000,
        likes: Math.floor(Math.random() * 1000) + 50,
        shares: Math.floor(Math.random() * 100) + 5,
        comments: Math.floor(Math.random() * 200) + 10,
      },
      sentiment: ["positive", "neutral", "negative"][i % 3] as
        | "positive"
        | "neutral"
        | "negative",
      category: ["政治", "娛樂", "科技", "體育"][i % 4],
      tags: [`標籤${i + 1}`, `分類${(i % 3) + 1}`],
      url: `https://example.com/post/${i + 3}`,
      mediaType: ["text", "image", "video"][i % 3] as
        | "text"
        | "image"
        | "video",
      verified: i % 4 === 0,
      clusterId: cluster.id,
      sourceId: source.id,
      platform: source.name,
      platformIcon: getSourceIcon(source.name),
      board: board?.name,
      boardId: board?.id,
    };
  });

  return [...baseArticles, ...additionalArticles] as ArticleItem[];
};
