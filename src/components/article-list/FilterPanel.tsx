import { ClusterFilter } from "./filters/ClusterFilter";
import { SentimentFilter } from "./filters/SentimentFilter";
import { DateRangeFilter } from "./filters/DateRangeFilter";
import { CategoryFilter } from "./filters/CategoryFilter";
import type { FilterPanelProps } from "./types";

export function FilterPanel({
  filters,
  onFilterChange,
  onArrayFilterChange,
  isVisible,
  availableClusters,
  sentimentOptions,
  categoryOptions,
  expandedClusters,
  expandedSources,
  onToggleClusterExpansion,
  onToggleSourceExpansion,
}: Readonly<FilterPanelProps>) {
  if (!isVisible) return null;

  return (
    <div className="bg-gray-50 p-6 border-b">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* 板塊篩選 - 使用與主題分析相同的結構 */}
        <ClusterFilter
          availableClusters={availableClusters}
          selectedClusters={filters.clusters}
          selectedSources={filters.sources}
          selectedBoards={filters.boards}
          onFilterChange={onArrayFilterChange}
          expandedClusters={expandedClusters}
          expandedSources={expandedSources}
          onToggleClusterExpansion={onToggleClusterExpansion}
          onToggleSourceExpansion={onToggleSourceExpansion}
        />

        <div className="space-y-4">
          <SentimentFilter
            sentimentOptions={sentimentOptions}
            selectedSentiments={filters.sentiments}
            onFilterChange={onArrayFilterChange}
          />

          <DateRangeFilter
            dateRange={filters.dateRange}
            onDateRangeChange={(dateRange) =>
              onFilterChange("dateRange", dateRange)
            }
          />

          <CategoryFilter
            categoryOptions={categoryOptions}
            selectedCategories={filters.categories}
            onFilterChange={onArrayFilterChange}
          />
        </div>
      </div>
    </div>
  );
}
