import { But<PERSON> } from "@/components/ui/button";
import {
  ChevronDown,
  ChevronUp,
  Download,
  FileText,
  Filter,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ArticleListHeaderProps } from "./types";

export function ArticleListHeader({
  onToggleFilters,
  showFilters,
}: Readonly<ArticleListHeaderProps>) {
  const { t } = useTranslation("articleList");
  const { t: common } = useTranslation("common");

  return (
    <div className="p-6 border-b">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <FileText className="w-6 h-6 mr-2" />
            {t("title")}
          </h1>
          <p className="text-gray-600 mt-1">{t("subtitle")}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            {common("export")}
          </Button>
          <Button variant="outline" size="sm" onClick={onToggleFilters}>
            <Filter className="w-4 h-4 mr-2" />
            {common("filter")}{" "}
            {showFilters ? (
              <ChevronUp className="w-4 h-4 ml-1" />
            ) : (
              <ChevronDown className="w-4 h-4 ml-1" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
