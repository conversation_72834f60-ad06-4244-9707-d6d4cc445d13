import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslation } from "react-i18next";
import type { SelectionControlsProps } from "./types";

export function SelectionControls({
  selectedCount,
  totalCount,
  onSelectAll,
  filterStats,
}: Readonly<SelectionControlsProps>) {
  const { t } = useTranslation("articleList", {
    keyPrefix: "controls",
  });

  return (
    <div className="flex items-center space-x-4">
      <div className="flex items-center space-x-2">
        <Checkbox
          checked={selectedCount === totalCount && totalCount > 0}
          onCheckedChange={(checked) => onSelectAll(checked as boolean)}
        />
        <span className="text-sm text-gray-600">
          {t("selected")} {selectedCount} {t("items")}
        </span>
      </div>

      {/* 顯示篩選統計 */}
      {(filterStats.clustersCount > 0 ||
        filterStats.sourcesCount > 0 ||
        filterStats.boardsCount > 0) && (
        <div className="flex items-center space-x-2">
          {filterStats.clustersCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {filterStats.clustersCount} {t("clusters")}
            </Badge>
          )}
          {filterStats.sourcesCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {filterStats.sourcesCount} {t("sources")}
            </Badge>
          )}
          {filterStats.boardsCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {filterStats.boardsCount} {t("boards")}
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
