import type { Cluster, SentimentOption, CategoryOption } from "./types";

// 與主題分析完全相同的資料結構
export const availableClusters: Cluster[] = [
  {
    id: "1",
    name: "主流新聞媒體(日報用)",
    description: "包含主要新聞媒體的日報監測",
    sourceCount: 38,
    excludeCount: 0,
    totalCount: 50,
    status: "active",
    sources: [
      { id: "udn", name: "聯合新聞網", description: "台灣主要新聞媒體" },
      { id: "ltn", name: "自由時報", description: "台灣主要新聞媒體" },
      { id: "chinatimes", name: "中時新聞網", description: "台灣主要新聞媒體" },
      { id: "ettoday", name: "ETtoday", description: "台灣網路新聞媒體" },
      { id: "tvbs", name: "TVBS新聞", description: "台灣電視新聞媒體" },
    ],
  },
  {
    id: "2",
    name: "社群媒體平台",
    description: "主要社群媒體平台監測",
    sourceCount: 15,
    excludeCount: 2,
    totalCount: 50,
    status: "active",
    sources: [
      {
        id: "facebook",
        name: "Facebook",
        description: "全球最大社群媒體平台",
        memberCount: 29000000,
      },
      {
        id: "instagram",
        name: "Instagram",
        description: "圖片分享社群平台",
        memberCount: 12000000,
      },
      {
        id: "twitter",
        name: "Twitter",
        description: "微博客社群平台",
        memberCount: 8000000,
      },
      {
        id: "tiktok",
        name: "TikTok",
        description: "短影片分享平台",
        memberCount: 15000000,
      },
      {
        id: "youtube",
        name: "YouTube",
        description: "影片分享平台",
        memberCount: 20000000,
      },
    ],
  },
  {
    id: "3",
    name: "論壇討論區",
    description: "熱門論壇和討論區",
    sourceCount: 12,
    excludeCount: 1,
    totalCount: 50,
    status: "active",
    sources: [
      {
        id: "ptt",
        name: "PTT",
        description: "台灣最大BBS論壇",
        memberCount: 1500000,
        boards: [
          {
            id: "gossiping",
            name: "Gossiping八卦板",
            description: "時事討論與八卦分享",
            memberCount: 180000,
            postCount: 50000,
          },
          {
            id: "stock",
            name: "Stock股票板",
            description: "股票投資討論",
            memberCount: 120000,
            postCount: 30000,
          },
          {
            id: "c_chat",
            name: "C_Chat西洽板 / 漫畫動畫板",
            description: "ACG文化討論",
            memberCount: 95000,
            postCount: 25000,
          },
          {
            id: "hatepolitics",
            name: "HatePolitics政黑板",
            description: "政治議題討論",
            memberCount: 85000,
            postCount: 40000,
          },
          {
            id: "womentalk",
            name: "WomenTalk女孩板",
            description: "女性議題討論",
            memberCount: 75000,
            postCount: 20000,
          },
          {
            id: "baseball",
            name: "Baseball棒球板",
            description: "棒球運動討論",
            memberCount: 65000,
            postCount: 15000,
          },
          {
            id: "nba",
            name: "NBA籃球板",
            description: "NBA籃球討論",
            memberCount: 55000,
            postCount: 12000,
          },
          {
            id: "tech_job",
            name: "Tech_Job科技板",
            description: "科技業工作討論",
            memberCount: 45000,
            postCount: 8000,
          },
          {
            id: "lifeismoney",
            name: "Lifeismoney省錢板",
            description: "省錢購物討論",
            memberCount: 40000,
            postCount: 10000,
          },
          {
            id: "marriage",
            name: "Marriage婚姻板",
            description: "婚姻關係討論",
            memberCount: 35000,
            postCount: 6000,
          },
        ],
      },
      {
        id: "dcard",
        name: "Dcard",
        description: "大學生社群平台",
        memberCount: 800000,
        boards: [
          {
            id: "funny",
            name: "有趣板",
            description: "有趣內容分享",
            memberCount: 150000,
            postCount: 20000,
          },
          {
            id: "relationship",
            name: "感情板",
            description: "感情問題討論",
            memberCount: 120000,
            postCount: 18000,
          },
          {
            id: "mood",
            name: "心情板",
            description: "心情分享",
            memberCount: 100000,
            postCount: 15000,
          },
          {
            id: "work",
            name: "工作板",
            description: "職場經驗分享",
            memberCount: 90000,
            postCount: 12000,
          },
          {
            id: "beauty",
            name: "美妝板",
            description: "美妝保養討論",
            memberCount: 80000,
            postCount: 10000,
          },
          {
            id: "food",
            name: "美食板",
            description: "美食分享",
            memberCount: 70000,
            postCount: 8000,
          },
        ],
      },
      {
        id: "mobile01",
        name: "Mobile01",
        description: "3C科技討論區",
        memberCount: 600000,
        boards: [
          {
            id: "smartphone",
            name: "智慧型手機",
            description: "手機討論區",
            memberCount: 80000,
            postCount: 12000,
          },
          {
            id: "computer",
            name: "電腦討論區",
            description: "電腦硬體軟體討論",
            memberCount: 70000,
            postCount: 10000,
          },
          {
            id: "car",
            name: "汽車討論區",
            description: "汽車相關討論",
            memberCount: 60000,
            postCount: 8000,
          },
          {
            id: "camera",
            name: "相機討論區",
            description: "攝影器材討論",
            memberCount: 50000,
            postCount: 6000,
          },
        ],
      },
      {
        id: "bahamut",
        name: "巴哈姆特",
        description: "遊戲動漫討論區",
        memberCount: 500000,
        boards: [
          {
            id: "gnn",
            name: "GNN新聞",
            description: "遊戲新聞討論",
            memberCount: 100000,
            postCount: 15000,
          },
          {
            id: "c_chat2",
            name: "場外休憩區",
            description: "綜合討論區",
            memberCount: 80000,
            postCount: 20000,
          },
          {
            id: "anime",
            name: "動畫討論區",
            description: "動畫作品討論",
            memberCount: 60000,
            postCount: 8000,
          },
          {
            id: "game",
            name: "遊戲討論區",
            description: "遊戲作品討論",
            memberCount: 70000,
            postCount: 12000,
          },
        ],
      },
    ],
  },
  {
    id: "4",
    name: "評價平台",
    description: "商品和服務評價平台",
    sourceCount: 8,
    excludeCount: 0,
    totalCount: 50,
    status: "active",
    sources: [
      {
        id: "google_review",
        name: "Google評論",
        description: "Google商家評論",
      },
      { id: "app_store", name: "App Store", description: "iOS應用程式商店" },
      {
        id: "google_play",
        name: "Google Play",
        description: "Android應用程式商店",
      },
      { id: "tripadvisor", name: "Tripadvisor", description: "旅遊評價平台" },
    ],
  },
  {
    id: "5",
    name: "部落格平台",
    description: "個人部落格和專業媒體",
    sourceCount: 25,
    excludeCount: 3,
    totalCount: 50,
    status: "paused",
    sources: [
      { id: "pixnet", name: "痞客邦", description: "台灣最大部落格平台" },
      { id: "medium", name: "Medium", description: "專業寫作平台" },
      {
        id: "personal_blog",
        name: "個人部落格",
        description: "各種個人部落格",
      },
      {
        id: "professional_media",
        name: "專業媒體",
        description: "專業媒體網站",
      },
    ],
  },
];

export const sentimentOptions: SentimentOption[] = [
  { value: "positive", label: "正面", color: "bg-green-500" },
  { value: "neutral", label: "中立", color: "bg-gray-500" },
  { value: "negative", label: "負面", color: "bg-red-500" },
];

export const categoryOptions: CategoryOption[] = [
  { value: "政治", label: "政治" },
  { value: "娛樂", label: "娛樂" },
  { value: "科技", label: "科技" },
  { value: "體育", label: "體育" },
  { value: "社會", label: "社會" },
  { value: "國際", label: "國際" },
  { value: "財經", label: "財經" },
  { value: "生活", label: "生活" },
];
