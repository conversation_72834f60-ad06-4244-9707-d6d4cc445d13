import * as React from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>riangle, Loader2 } from "lucide-react";

export interface ConfirmDialogProps {
  /**
   * Controls the open/closed state of the dialog
   */
  open: boolean;
  /**
   * Callback when the dialog should be closed
   */
  onOpenChange: (open: boolean) => void;
  /**
   * The title of the dialog
   */
  title: string;
  /**
   * The description or message to show in the dialog
   */
  description: string;
  /**
   * The text to show on the confirm button (default: "Confirm")
   */
  confirmText?: string;
  /**
   * The text to show on the cancel button (default: "Cancel")
   */
  cancelText?: string;
  /**
   * The variant of the confirm button (default: "destructive")
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | null | undefined;
  /**
   * Whether the action is in progress (shows a loading spinner)
   */
  isLoading?: boolean;
  /**
   * Callback when the confirm button is clicked
   */
  onConfirm: () => void | Promise<void>;
  /**
   * Additional class name for the dialog content
   */
  className?: string;
}

/**
 * A reusable confirmation dialog component that can be used for delete confirmations
 * and other destructive actions.
 */
export function ConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText,
  cancelText,
  variant = "destructive",
  isLoading = false,
  onConfirm,
  className,
}: ConfirmDialogProps) {
  const { t } = useTranslation("common");
  const [isInternalLoading, setIsInternalLoading] = React.useState(false);

  const handleConfirm = async () => {
    try {
      setIsInternalLoading(true);
      await Promise.resolve(onConfirm());
      onOpenChange(false);
    } catch (error) {
      console.error("Error in confirm action:", error);
    } finally {
      setIsInternalLoading(false);
    }
  };

  const loading = isLoading || isInternalLoading;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={className}>
        <DialogHeader>
          <div className="flex flex-col items-center text-center sm:text-left">
            <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <AlertTriangle className="h-6 w-6 text-red-600" aria-hidden="true" />
            </div>
            <DialogTitle className="mt-3 text-lg font-medium text-foreground">
              {title}
            </DialogTitle>
          </div>
          <DialogDescription className="text-center sm:text-left">
            {description}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-col-reverse gap-3 sm:flex-row sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
            className="w-full sm:w-auto"
          >
            {cancelText || t("cancel")}
          </Button>
          <Button
            type="button"
            variant={variant}
            onClick={handleConfirm}
            disabled={loading}
            className="w-full sm:w-auto"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {confirmText || t("confirm")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
