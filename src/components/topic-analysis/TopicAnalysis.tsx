import { TopicAnalysisResults } from "@/components/topic-analysis-results/topic-analysis-results";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { AnalysisRecordsSidebar } from "./components/AnalysisRecordsSidebar";
import { SearchForm } from "./components/SearchForm";
import { initialAnalysisRecords, summaryTemplates } from "./constants";
import { useAnalysisRecords } from "./hooks/useAnalysisRecords";
import { useSearchForm } from "./hooks/useSearchForm";
import { useUrlParams } from "./hooks/useUrlParams";
import type { AnalysisRecord } from "./types";

export function TopicAnalysis() {
  const { t } = useTranslation("topicAnalysis");

  // Custom hooks for state management
  const { queryParam, showResultsParam } = useUrlParams();
  const {
    analysisRecords,
    setAnalysisRecords,
    currentAnalysis,
    setCurrentAnalysis,
    handleRecordClick,
    handleClearRecord,
  } = useAnalysisRecords();

  const {
    searchQuery,
    setSearchQuery,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    selectedClusters,
    setSelectedClusters,
    selectedSources,
    setSelectedSources,
    selectedBoards,
    setSelectedBoards,
    selectedKeywordGroups,
    setSelectedKeywordGroups,
    showClusterSelector,
    setShowClusterSelector,
    showKeywordSelector,
    setShowKeywordSelector,
    isLoading,
    setIsLoading,
  } = useSearchForm(queryParam || "", "2024-06-01", "2025-05-31");

  // Local state
  const [showResults, setShowResults] = useState(showResultsParam === "true");
  const [searchTerm, setSearchTerm] = useState("");

  // Helper functions
  const generateSummary = useCallback((): string => {
    return summaryTemplates[
      Math.floor(Math.random() * summaryTemplates.length)
    ];
  }, []);

  const handleSearch = () => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);

    // Add to analysis records
    const newAnalysisRecord: AnalysisRecord = {
      id: Date.now().toString(),
      query: searchQuery,
      timestamp: new Date(),
      resultCount: Math.floor(Math.random() * 3000) + 1000,
      status: "analyzing",
      summary: t("messages.analyzingInProgress"),
      clusters: selectedClusters,
      keywordGroups: selectedKeywordGroups,
      dateRange: {
        start: startDate,
        end: endDate,
      },
      selectedSources: selectedSources,
      selectedBoards: selectedBoards,
    };

    setAnalysisRecords((prev) => [newAnalysisRecord, ...prev]);
    setCurrentAnalysis(newAnalysisRecord);

    // Simulate search delay
    setTimeout(() => {
      const completedRecord = {
        ...newAnalysisRecord,
        status: "completed" as const,
        summary: generateSummary(),
      };

      setAnalysisRecords((prev) =>
        prev.map((record) =>
          record.id === newAnalysisRecord.id ? completedRecord : record,
        ),
      );
      setCurrentAnalysis(completedRecord);
      setIsLoading(false);
      setShowResults(true);
    }, 2000);
  };

  const handleRecordClickWithState = (record: AnalysisRecord) => {
    handleRecordClick(record);
    if (record.status === "completed") {
      setSearchQuery(record.query);
      setSelectedClusters(record.clusters);
      setSelectedSources(record.selectedSources || {});
      setSelectedBoards(record.selectedBoards || {});
      setShowResults(true);
    }
  };

  const handleSuggestedTopicClick = (topic: string) => {
    setSearchQuery(topic);
  };

  const handleSourceChange = (clusterId: string, sourceIds: string[]) => {
    setSelectedSources((prev) => ({
      ...prev,
      [clusterId]: sourceIds,
    }));
  };

  const handleBoardChange = (sourceId: string, boardIds: string[]) => {
    setSelectedBoards((prev) => ({
      ...prev,
      [sourceId]: boardIds,
    }));
  };

  const startNewAnalysis = () => {
    setCurrentAnalysis(null);
    setSearchQuery("");
    setShowResults(false);
  };

  // URL parameter handling effect
  useEffect(() => {
    if (queryParam && showResultsParam === "true") {
      // Find corresponding analysis record
      const existingRecord = initialAnalysisRecords.find(
        (record) => record.query === queryParam,
      );
      if (existingRecord) {
        setCurrentAnalysis(existingRecord);
        setSelectedClusters(existingRecord.clusters);
        setSelectedSources(existingRecord.selectedSources || {});
        setSelectedBoards(existingRecord.selectedBoards || {});
        setShowResults(true);
      } else {
        // 如果沒有找到記錄，創建一個新的
        const newRecord: AnalysisRecord = {
          id: Date.now().toString(),
          query: queryParam,
          timestamp: new Date(),
          resultCount: Math.floor(Math.random() * 30000) + 1000,
          status: "completed",
          summary: generateSummary(),
          clusters: ["1", "2"],
          keywordGroups: [1, 2],
          dateRange: {
            start: startDate,
            end: endDate,
          },
        };
        setCurrentAnalysis(newRecord);
        setAnalysisRecords((prev) => [newRecord, ...prev]);
        setShowResults(true);
      }
    }
  }, [
    queryParam,
    showResultsParam,
    startDate,
    endDate,
    setCurrentAnalysis,
    setSelectedClusters,
    setSelectedSources,
    setSelectedBoards,
    setShowResults,
    setAnalysisRecords,
    generateSummary,
  ]);

  return (
    <div className="flex h-[calc(100vh-73px)]">
      {/* Left Sidebar - Analysis Records */}
      <AnalysisRecordsSidebar
        analysisRecords={analysisRecords}
        currentAnalysis={currentAnalysis}
        searchTerm={searchTerm}
        onSearchTermChange={setSearchTerm}
        onRecordClick={handleRecordClickWithState}
        onClearRecord={handleClearRecord}
        onStartNewAnalysis={startNewAnalysis}
        onSuggestedTopicClick={handleSuggestedTopicClick}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {!showResults ? (
          /* Search Form */
          <SearchForm
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            selectedClusters={selectedClusters}
            onClusterChange={setSelectedClusters}
            selectedSources={selectedSources}
            onSourceChange={handleSourceChange}
            selectedBoards={selectedBoards}
            onBoardChange={handleBoardChange}
            selectedKeywordGroups={selectedKeywordGroups}
            onKeywordGroupChange={setSelectedKeywordGroups}
            showClusterSelector={showClusterSelector}
            onToggleClusterSelector={() =>
              setShowClusterSelector(!showClusterSelector)
            }
            showKeywordSelector={showKeywordSelector}
            onToggleKeywordSelector={() =>
              setShowKeywordSelector(!showKeywordSelector)
            }
            isLoading={isLoading}
            onSearch={handleSearch}
            onSuggestedTopicClick={handleSuggestedTopicClick}
          />
        ) : !currentAnalysis ? (
          <div className="flex items-center justify-center h-full">
            <p>No analysis data available</p>
          </div>
        ) : (
          <TopicAnalysisResults
            query={currentAnalysis.query}
            result={{
              volumeTrend: [],
              sourceDistribution: [],
              sentimentData: {
                totalPositive: 0,
                totalNeutral: 0,
                totalNegative: 0,
                overallRatio: 0,
              },
              wordCloud: [],
              topPosts: [],
              platformAnalysis: [],
              hotTopics: [],
              hotArticles: [],
              mediaSourceCategories: [],
              topMediaSources: [],
              platformActivity: [],
              resultCount: currentAnalysis.resultCount,
              timestamp: currentAnalysis.timestamp.toISOString(),
            }}
            onBack={startNewAnalysis}
          />
        )}
      </div>
    </div>
  );
}
