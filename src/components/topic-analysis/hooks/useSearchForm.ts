import { useState } from "react";
import type { UseSearchFormReturn } from "../types";

export function useSearchForm(
  initialQuery = "",
  initialStartDate = "2024-06-01",
  initialEndDate = "2025-05-31",
): UseSearchFormReturn {
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  const [selectedClusters, setSelectedClusters] = useState<string[]>([
    "1",
    "2",
  ]);
  const [selectedSources, setSelectedSources] = useState<{
    [clusterId: string]: string[];
  }>({});
  const [selectedBoards, setSelectedBoards] = useState<{
    [sourceId: string]: string[];
  }>({});
  const [selectedKeywordGroups, setSelectedKeywordGroups] = useState<number[]>([
    1, 2,
  ]);
  const [showClusterSelector, setShowClusterSelector] = useState(true);
  const [showKeywordSelector, setShowKeywordSelector] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  return {
    searchQuery,
    setSearchQuery,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    selectedClusters,
    setSelectedClusters,
    selectedSources,
    setSelectedSources,
    selectedBoards,
    setSelectedBoards,
    selectedKeywordGroups,
    setSelectedKeywordGroups,
    showClusterSelector,
    setShowClusterSelector,
    showKeywordSelector,
    setShowKeywordSelector,
    isLoading,
    setIsLoading,
  };
}
