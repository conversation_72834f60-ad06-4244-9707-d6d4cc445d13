import { useState } from "react";
import type { AnalysisRecord, UseAnalysisRecordsReturn } from "../types";
import { initialAnalysisRecords } from "../constants";

export function useAnalysisRecords(): UseAnalysisRecordsReturn {
  const [analysisRecords, setAnalysisRecords] = useState<AnalysisRecord[]>(
    initialAnalysisRecords,
  );
  const [currentAnalysis, setCurrentAnalysis] = useState<AnalysisRecord | null>(
    null,
  );

  const handleRecordClick = (record: AnalysisRecord) => {
    if (record.status === "completed") {
      setCurrentAnalysis(record);
    }
  };

  const handleClearRecord = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    setAnalysisRecords((prev) => prev.filter((record) => record.id !== id));
  };

  return {
    analysisRecords,
    setAnalysisRecords,
    currentAnalysis,
    setCurrentAnalysis,
    handleRecord<PERSON>lick,
    handleClearRecord,
  };
}
