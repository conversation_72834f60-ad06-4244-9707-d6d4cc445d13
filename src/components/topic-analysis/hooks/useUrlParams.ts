import { useState } from "react";
import type { UseUrlParamsReturn } from "../types";

export function useUrlParams(): UseUrlParamsReturn {
  const [searchParams, setSearchParams] = useState(() => {
    if (typeof window !== "undefined") {
      return new URLSearchParams(window.location.search);
    }
    return new URLSearchParams();
  });

  const queryParam = searchParams.get("query");
  const showResultsParam = searchParams.get("showResults");

  return {
    searchParams,
    setSearchParams,
    queryParam,
    showResultsParam,
  };
}
