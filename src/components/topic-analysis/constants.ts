import type { AnalysisRecord } from "./types";

export const suggestedTopics = [
  "iPhone 15 Pro Max",
  "Samsung Galaxy S25",
  "Google Pixel 9",
  "小米 14 Ultra",
  "OPPO Find X7",
  "Vivo X100 Pro",
  "華為 Mate 60 Pro",
  "Sony Xperia 1 VI",
];

export const keywordGroups = [
  { id: 1, name: "性能" },
  { id: 2, name: "價格" },
  { id: 3, name: "設計" },
];

// 初始分析記錄
export const initialAnalysisRecords: AnalysisRecord[] = [
  {
    id: "1",
    query: "黃建賓",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    resultCount: 30927,
    status: "completed",
    summary: "政治相關討論為主，情緒好感度為0.34",
    clusters: ["3"],
    keywordGroups: [1, 3],
    dateRange: {
      start: "2024-06-01",
      end: "2025-05-31",
    },
    selectedSources: {
      "3": ["ptt", "dcard"],
    },
    selectedBoards: {
      ptt: ["gossiping", "hatepolitics"],
      dcard: ["mood", "funny"],
    },
  },
  {
    id: "2",
    query: "Samsung Galaxy S25",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    resultCount: 3187,
    status: "completed",
    summary: "相機功能受到好評，充電速度有待改善",
    clusters: ["2", "3"],
    keywordGroups: [2, 4],
    dateRange: {
      start: "2025-01-10",
      end: "2025-01-20",
    },
    selectedSources: {
      "2": ["facebook", "instagram"],
      "3": ["ptt", "mobile01"],
    },
    selectedBoards: {
      ptt: ["c_chat", "tech_job"],
      mobile01: ["smartphone", "camera"],
    },
  },
  {
    id: "3",
    query: "Google Pixel 9",
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    resultCount: 1845,
    status: "completed",
    summary: "AI功能創新獲得認可，但市場可見度不足",
    clusters: ["1", "4"],
    keywordGroups: [1],
    dateRange: {
      start: "2025-01-05",
      end: "2025-01-18",
    },
    selectedSources: {
      "1": ["udn", "ettoday"],
      "4": ["google_review", "app_store"],
    },
  },
];

export const statusColorMap = {
  completed: "bg-green-100 text-green-800",
  analyzing: "bg-blue-100 text-blue-800",
  failed: "bg-red-100 text-red-800",
  default: "bg-gray-100 text-gray-800",
} as const;

// Status text is now handled by i18n translations in topicAnalysis.status
// Removed statusTextMap - use translations instead

export const summaryTemplates = [
  "整體評價正面，但價格成為主要討論焦點",
  "功能創新獲得認可，使用體驗有待改善",
  "性價比優勢明顯，品牌認知度需要提升",
  "設計美學受到讚賞，技術規格表現亮眼",
  "市場反應熱烈，部分功能仍需優化",
];
