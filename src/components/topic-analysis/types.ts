export interface AnalysisRecord {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
  status: "completed" | "analyzing" | "failed";
  summary: string;
  clusters: string[];
  keywordGroups: number[];
  dateRange: {
    start: string;
    end: string;
  };
  selectedSources?: { [clusterId: string]: string[] };
  selectedBoards?: { [sourceId: string]: string[] };
}

export interface AnalysisRecordsSidebarProps {
  analysisRecords: AnalysisRecord[];
  currentAnalysis: AnalysisRecord | null;
  searchTerm: string;
  onSearchTermChange: (term: string) => void;
  onRecordClick: (record: AnalysisRecord) => void;
  onClearRecord: (e: React.MouseEvent, id: string) => void;
  onStartNewAnalysis: () => void;
  onSuggestedTopicClick: (topic: string) => void;
}

export interface SearchFormProps {
  searchQuery: string;
  onSearchQueryChange: (query: string) => void;
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  selectedClusters: string[];
  onClusterChange: (clusters: string[]) => void;
  selectedSources: { [clusterId: string]: string[] };
  onSourceChange: (clusterId: string, sourceIds: string[]) => void;
  selectedBoards: { [sourceId: string]: string[] };
  onBoardChange: (sourceId: string, boardIds: string[]) => void;
  selectedKeywordGroups: number[];
  onKeywordGroupChange: (groups: number[]) => void;
  showClusterSelector: boolean;
  onToggleClusterSelector: () => void;
  showKeywordSelector: boolean;
  onToggleKeywordSelector: () => void;
  isLoading: boolean;
  onSearch: () => void;
  onSuggestedTopicClick: (topic: string) => void;
}

export interface AnalysisRecordProps {
  record: AnalysisRecord;
  isSelected: boolean;
  onClick: () => void;
  onClear: (e: React.MouseEvent) => void;
}

export interface SuggestedTopicsProps {
  topics?: string[];
  onTopicClick: (topic: string) => void;
  className?: string;
  variant?: "default" | "compact";
}

export interface DateRangeSelectorProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
}

export interface FeatureCardsProps {
  className?: string;
}

export interface UseAnalysisRecordsReturn {
  analysisRecords: AnalysisRecord[];
  setAnalysisRecords: React.Dispatch<React.SetStateAction<AnalysisRecord[]>>;
  currentAnalysis: AnalysisRecord | null;
  setCurrentAnalysis: React.Dispatch<
    React.SetStateAction<AnalysisRecord | null>
  >;
  handleRecordClick: (record: AnalysisRecord) => void;
  handleClearRecord: (e: React.MouseEvent, id: string) => void;
}

export interface UseSearchFormReturn {
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  startDate: string;
  setStartDate: React.Dispatch<React.SetStateAction<string>>;
  endDate: string;
  setEndDate: React.Dispatch<React.SetStateAction<string>>;
  selectedClusters: string[];
  setSelectedClusters: React.Dispatch<React.SetStateAction<string[]>>;
  selectedSources: { [clusterId: string]: string[] };
  setSelectedSources: React.Dispatch<
    React.SetStateAction<{ [clusterId: string]: string[] }>
  >;
  selectedBoards: { [sourceId: string]: string[] };
  setSelectedBoards: React.Dispatch<
    React.SetStateAction<{ [sourceId: string]: string[] }>
  >;
  selectedKeywordGroups: number[];
  setSelectedKeywordGroups: React.Dispatch<React.SetStateAction<number[]>>;
  showClusterSelector: boolean;
  setShowClusterSelector: React.Dispatch<React.SetStateAction<boolean>>;
  showKeywordSelector: boolean;
  setShowKeywordSelector: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface UseUrlParamsReturn {
  searchParams: URLSearchParams;
  setSearchParams: React.Dispatch<React.SetStateAction<URLSearchParams>>;
  queryParam: string | null;
  showResultsParam: string | null;
}
