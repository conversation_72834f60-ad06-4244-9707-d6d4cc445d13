import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FileText, Plus, Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { AnalysisRecordsSidebarProps } from "../types";
import { AnalysisRecord } from "./AnalysisRecord";
import { SuggestedTopics } from "./SuggestedTopics";

export function AnalysisRecordsSidebar({
  analysisRecords,
  currentAnalysis,
  searchTerm,
  onSearchTermChange,
  onRecordClick,
  onClearRecord,
  onStartNewAnalysis,
  onSuggestedTopicClick,
}: Readonly<AnalysisRecordsSidebarProps>) {
  const { t } = useTranslation("topicAnalysis");

  const filteredRecords = analysisRecords.filter((record) =>
    record.query.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col h-full">
      <div className="p-4 border-b">
        <Button
          onClick={onStartNewAnalysis}
          className="w-full bg-primary hover:bg-primary-600"
        >
          <Plus className="w-4 h-4 mr-2" />
          {t("newAnalysis")}
        </Button>
      </div>

      <div className="p-4 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder={t("searchRecords")}
            className="pl-10"
            value={searchTerm}
            onChange={(e) => onSearchTermChange(e.target.value)}
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          <h3 className="text-sm font-medium text-gray-500 mb-2 px-2">
            {t("title")}
          </h3>
          <div className="space-y-2">
            {filteredRecords.map((record) => (
              <AnalysisRecord
                key={record.id}
                record={record}
                isSelected={currentAnalysis?.id === record.id}
                onClick={() => onRecordClick(record)}
                onClear={(e) => onClearRecord(e, record.id)}
              />
            ))}
          </div>

          {filteredRecords.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">{t("noRecords")}</p>
              <p className="text-xs">{t("startFirstAnalysis")}</p>
            </div>
          )}

          <h3 className="text-sm font-medium text-gray-500 mt-6 mb-2 px-2">
            {t("hotTopics")}
          </h3>
          <SuggestedTopics
            onTopicClick={onSuggestedTopicClick}
            variant="compact"
          />
        </div>
      </div>
    </div>
  );
}
