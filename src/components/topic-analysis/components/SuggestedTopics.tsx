import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Spark<PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";
import { suggestedTopics } from "../constants";
import type { SuggestedTopicsProps } from "../types";

export function SuggestedTopics({
  topics = suggestedTopics,
  onTopicClick,
  className,
  variant = "default",
}: Readonly<SuggestedTopicsProps>) {
  const { t } = useTranslation("topicAnalysis");

  if (variant === "compact") {
    return (
      <div className={cn("px-2", className)}>
        <div className="flex flex-wrap gap-2">
          {topics.slice(0, 6).map((topic, index) => (
            <Badge
              key={index}
              variant="outline"
              className="cursor-pointer hover:bg-primary-50 hover:border-primary text-xs"
              onClick={() => onTopicClick(topic)}
            >
              {topic}
            </Badge>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("mb-6", className)}>
      <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
        <Sparkles className="w-4 h-4 mr-2" />
        {t("hotTopics")}
      </h3>
      <div className="flex flex-wrap gap-2">
        {topics.map((topic, index) => (
          <Badge
            key={index}
            variant="outline"
            className="cursor-pointer hover:bg-primary-50 hover:border-primary text-sm py-2 px-3"
            onClick={() => onTopicClick(topic)}
          >
            {topic}
          </Badge>
        ))}
      </div>
    </div>
  );
}
