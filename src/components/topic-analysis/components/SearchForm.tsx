import { ClusterSelector } from "@/components/cluster-selector";
import { KeywordSelector } from "@/components/keyword-selector";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { SearchFormProps } from "../types";
import { DateRangeSelector } from "./DateRangeSelector";
import { FeatureCards } from "./FeatureCards";
import { SuggestedTopics } from "./SuggestedTopics";

export function SearchForm({
  searchQuery,
  onSearchQueryChange,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  selectedClusters,
  onClusterChange,
  selectedSources,
  onSourceChange,
  selectedBoards,
  onBoardChange,
  selectedKeywordGroups,
  onKeywordGroupChange,
  showClusterSelector,
  onToggleClusterSelector,
  showKeywordSelector,
  onToggleKeywordSelector,
  isLoading,
  onSearch,
  onSuggestedTopicClick,
}: Readonly<SearchFormProps>) {
  const { t } = useTranslation("topicAnalysis");

  return (
    <div className="flex-1 p-6 overflow-y-auto">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="w-8 h-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t("title")}
          </h1>
          <p className="text-gray-600">{t("description")}</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center space-x-2 mb-6">
            <Input
              value={searchQuery}
              onChange={(e) => onSearchQueryChange(e.target.value)}
              placeholder={t("inputPlaceholder")}
              className="flex-1"
              onKeyDown={(e) => e.key === "Enter" && onSearch()}
            />
            <Button
              onClick={onSearch}
              disabled={!searchQuery.trim() || isLoading}
            >
              {isLoading ? t("analyzing") : t("analyze")}
            </Button>
          </div>

          <DateRangeSelector
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={onStartDateChange}
            onEndDateChange={onEndDateChange}
          />

          <div className="mb-6">
            <ClusterSelector
              selectedClusters={selectedClusters}
              onClusterChange={onClusterChange}
              selectedSources={selectedSources}
              onSourceChange={onSourceChange}
              selectedBoards={selectedBoards}
              onBoardChange={onBoardChange}
              isCollapsed={!showClusterSelector}
              onToggleCollapse={onToggleClusterSelector}
            />
          </div>

          <div className="mb-6">
            <KeywordSelector
              selectedKeywordGroups={selectedKeywordGroups}
              onKeywordGroupChange={onKeywordGroupChange}
              isCollapsed={!showKeywordSelector}
              onToggleCollapse={onToggleKeywordSelector}
            />
          </div>

          <SuggestedTopics onTopicClick={onSuggestedTopicClick} />

          <FeatureCards />
        </div>
      </div>
    </div>
  );
}
