import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { FileText, MessageSquare, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { FeatureCardsProps } from "../types";

export function FeatureCards({ className }: Readonly<FeatureCardsProps>) {
  const { t } = useTranslation("topicAnalysis");

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-3 gap-4", className)}>
      <Card className="p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center space-x-3">
          <MessageSquare className="w-8 h-8 text-primary" />
          <div>
            <h4 className="font-medium">{t("features.discussionFocus")}</h4>
            <p className="text-sm text-gray-600">
              {t("features.discussionFocusDesc")}
            </p>
          </div>
        </div>
      </Card>
      <Card className="p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center space-x-3">
          <TrendingUp className="w-8 h-8 text-green-500" />
          <div>
            <h4 className="font-medium">{t("features.sentimentTracking")}</h4>
            <p className="text-sm text-gray-600">
              {t("features.sentimentTrackingDesc")}
            </p>
          </div>
        </div>
      </Card>
      <Card className="p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center space-x-3">
          <FileText className="w-8 h-8 text-yellow-500" />
          <div>
            <h4 className="font-medium">{t("features.hotArticles")}</h4>
            <p className="text-sm text-gray-600">
              {t("features.hotArticlesDesc")}
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}
