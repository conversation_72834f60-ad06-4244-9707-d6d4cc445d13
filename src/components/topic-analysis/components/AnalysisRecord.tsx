import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Clock, Target, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { statusColorMap } from "../constants";
import type { AnalysisRecordProps } from "../types";

export function AnalysisRecord({
  record,
  isSelected,
  onClick,
  onClear,
}: Readonly<AnalysisRecordProps>) {
  const { t } = useTranslation("topicAnalysis");
  const getStatusColor = (status: string) => {
    return (
      statusColorMap[status as keyof typeof statusColorMap] ||
      statusColorMap.default
    );
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return t("status.completed");
      case "analyzing":
        return t("status.analyzing");
      case "failed":
        return t("status.failed");
      default:
        return t("status.unknown");
    }
  };

  return (
    <div
      className={`p-3 rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
        isSelected
          ? "bg-primary-50 border border-primary-200"
          : "border border-transparent"
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <Target className="w-4 h-4 text-primary flex-shrink-0" />
            <h4 className="font-medium text-sm truncate">{record.query}</h4>
          </div>
          <div className="flex items-center justify-between mb-2">
            <Badge
              className={getStatusColor(record.status)}
              variant="secondary"
            >
              {getStatusText(record.status)}
            </Badge>
            <span className="text-xs text-gray-500">
              {record.resultCount.toLocaleString()}{" "}
              {t("analysisRecord.recordsUnit")}
            </span>
          </div>
        </div>
        {record.status === "completed" && (
          <Button
            variant="ghost"
            size="sm"
            className="opacity-0 group-hover:opacity-100 ml-2"
            onClick={onClear}
          >
            <X className="w-3 h-3" />
          </Button>
        )}
      </div>

      <p className="text-xs text-gray-600 mb-2 line-clamp-2">
        {record.summary}
      </p>

      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center">
          <Clock className="w-3 h-3 mr-1" />
          <span>{record.timestamp.toLocaleDateString()}</span>
        </div>
        <div className="flex items-center">
          <Calendar className="w-3 h-3 mr-1" />
          <span>{record.dateRange.start}</span>
        </div>
      </div>

      {/* Display selected sources and boards statistics */}
      {(record.selectedSources || record.selectedBoards) && (
        <div className="mt-2 flex flex-wrap gap-1">
          {record.selectedSources &&
            Object.values(record.selectedSources).reduce(
              (sum, sources) => sum + sources.length,
              0,
            ) > 0 && (
              <Badge variant="outline" className="text-xs">
                {Object.values(record.selectedSources).reduce(
                  (sum, sources) => sum + sources.length,
                  0,
                )}{" "}
                {t("analysisRecord.sources")}
              </Badge>
            )}
          {record.selectedBoards &&
            Object.values(record.selectedBoards).reduce(
              (sum, boards) => sum + boards.length,
              0,
            ) > 0 && (
              <Badge variant="secondary" className="text-xs">
                {Object.values(record.selectedBoards).reduce(
                  (sum, boards) => sum + boards.length,
                  0,
                )}{" "}
                {t("analysisRecord.boards")}
              </Badge>
            )}
        </div>
      )}
    </div>
  );
}
