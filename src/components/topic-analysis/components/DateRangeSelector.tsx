import { Input } from "@/components/ui/input";
import { Calendar } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { DateRangeSelectorProps } from "../types";

export function DateRangeSelector({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
}: Readonly<DateRangeSelectorProps>) {
  const { t } = useTranslation("topicAnalysis");
  const { t: common } = useTranslation("common");

  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
        <Calendar className="w-4 h-4 mr-2" />
        {t("dateRange")}
      </h3>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="start-date"
            className="text-sm font-medium mb-2 block"
          >
            {common("startDate")}
          </label>
          <Input
            id="start-date"
            type="date"
            value={startDate}
            onChange={(e) => onStartDateChange(e.target.value)}
          />
        </div>
        <div>
          <label htmlFor="end-date" className="text-sm font-medium mb-2 block">
            {common("endDate")}
          </label>
          <Input
            id="end-date"
            type="date"
            value={endDate}
            onChange={(e) => onEndDateChange(e.target.value)}
          />
        </div>
      </div>
    </div>
  );
}
