import { Skeleton } from "@/components/ui/skeleton";
import type { ReactNode } from "react";

type AdminPageSkeletonProps = {
  title?: string;
  description?: string;
  header?: boolean;
  children?: ReactNode;
};

export function AdminPageSkeleton({
  title = "Title",
  description = "Description",
  header = true,
  children,
}: AdminPageSkeletonProps) {
  return (
    <div className="space-y-6">
      {header && (
        <div className="flex flex-col space-y-2">
          {title && <Skeleton className="h-8 w-48 rounded-md" />}
          {description && <Skeleton className="h-4 w-64 rounded-md" />}
        </div>
      )}

      {children || (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton className="h-10 w-48 rounded-md" />
            <Skeleton className="h-10 w-32 rounded-md" />
          </div>

          <div className="rounded-lg border">
            <div className="space-y-4 p-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-4 rounded" />
                  <Skeleton className="h-4 flex-1 rounded" />
                  <Skeleton className="h-8 w-20 rounded" />
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between border-t px-4 py-3">
              <Skeleton className="h-4 w-24 rounded" />
              <div className="flex space-x-2">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
