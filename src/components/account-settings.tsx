import { LanguageSelector } from "@/components/language-selector";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import {
  Calendar,
  Cog,
  Edit,
  FileText,
  Filter,
  Globe,
  Mail,
  Shield,
  User,
} from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { GradientHeader } from "./gradient-header";

export function AccountSettings() {
  const { t: common } = useTranslation("common");
  const { t } = useTranslation("accountSettings");

  const [filterSettings, setFilterSettings] = useState({
    lowRelevance: false,
    advertising: false,
    lowContent: false,
    foreignLanguage: false,
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <GradientHeader
        icon={User}
        title={t("title")}
        description={t("description")}
      />

      {/* Profile Section */}
      <Card className="overflow-hidden">
        <div className="bg-gradient-to-r from-primary-400 to-secondary-400 h-32 relative">
          <div className="absolute -bottom-16 left-8">
            <Avatar className="w-32 h-32 border-4 border-white shadow-lg">
              <AvatarImage src="/placeholder.svg?height=128&width=128" />
              <AvatarFallback className="text-2xl bg-white text-primary">
                <User className="w-12 h-12" />
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
        <CardContent className="pt-20 pb-6">
          <div className="flex justify-between items-start">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <h2 className="text-2xl font-bold text-gray-900">
                  wavenet / admin
                </h2>
                <Badge className="bg-red-500 hover:bg-red-600">
                  <Shield className="w-3 h-3 mr-1" />
                  {t("profile.systemAdmin")}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center space-x-2 text-gray-600">
                  <Cog className="w-4 h-4" />
                  <span>{t("profile.permissions")}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Calendar className="w-4 h-4" />
                  <span>{t("profile.expiryDate")}</span>
                </div>
              </div>
            </div>
            <Button variant="outline">
              <Edit className="w-4 h-4 mr-2" />
              {common("edit")}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="w-5 h-5" />
            <span>{t("languageSettings.title")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              {t("languageSettings.description")}
            </p>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">
                  {t("languageSettings.currentLanguage")}
                </h4>
                <p className="text-sm text-gray-500">{common("language")}</p>
              </div>
              <LanguageSelector />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filter Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>{t("filterSettings.title")}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">
                  {t("filterSettings.lowRelevance.title")}
                </h4>
                <p className="text-sm text-gray-500">
                  {t("filterSettings.lowRelevance.description")}
                </p>
              </div>
              <Switch
                checked={filterSettings.lowRelevance}
                onCheckedChange={(checked) =>
                  setFilterSettings({
                    ...filterSettings,
                    lowRelevance: checked,
                  })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">
                  {t("filterSettings.lowContent.title")}
                </h4>
                <p className="text-sm text-gray-500">
                  {t("filterSettings.lowContent.description")}
                </p>
              </div>
              <Switch
                checked={filterSettings.lowContent}
                onCheckedChange={(checked) =>
                  setFilterSettings({ ...filterSettings, lowContent: checked })
                }
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="w-5 h-5" />
              <span>{t("articleTypeFilter.title")}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">
                  {t("articleTypeFilter.advertising.title")}
                </h4>
                <p className="text-sm text-gray-500">
                  {t("articleTypeFilter.advertising.description")}
                </p>
              </div>
              <Switch
                checked={filterSettings.advertising}
                onCheckedChange={(checked) =>
                  setFilterSettings({ ...filterSettings, advertising: checked })
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">
                  {t("articleTypeFilter.foreignLanguage.title")}
                </h4>
                <p className="text-sm text-gray-500">
                  {t("articleTypeFilter.foreignLanguage.description")}
                </p>
              </div>
              <Switch
                checked={filterSettings.foreignLanguage}
                onCheckedChange={(checked) =>
                  setFilterSettings({
                    ...filterSettings,
                    foreignLanguage: checked,
                  })
                }
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
