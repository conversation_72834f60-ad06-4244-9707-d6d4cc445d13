import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Clock, Hash, Plus, TrendingUp, X } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

interface Topic {
  id: string;
  name: string;
  type: "analysis" | "keyword";
  source: "comprehensive" | "topic" | "custom";
  lastAnalyzed?: string;
  volume?: number;
}

interface TopicSelectorProps {
  selectedTopics: string[];
  onTopicChange: (topicIds: string[]) => void;
  maxTopics?: number;
  minTopics?: number;
}

// 模擬的分析紀錄數據
const analysisHistory: Topic[] = [
  {
    id: "1",
    name: "電動車市場趨勢",
    type: "analysis",
    source: "comprehensive",
    lastAnalyzed: "2024-01-15",
    volume: 1250,
  },
  {
    id: "2",
    name: "AI人工智慧發展",
    type: "analysis",
    source: "topic",
    lastAnalyzed: "2024-01-14",
    volume: 2100,
  },
  {
    id: "3",
    name: "房價政策討論",
    type: "analysis",
    source: "comprehensive",
    lastAnalyzed: "2024-01-13",
    volume: 890,
  },
  {
    id: "4",
    name: "疫情後經濟復甦",
    type: "analysis",
    source: "topic",
    lastAnalyzed: "2024-01-12",
    volume: 1560,
  },
  {
    id: "5",
    name: "綠能政策推動",
    type: "analysis",
    source: "comprehensive",
    lastAnalyzed: "2024-01-11",
    volume: 720,
  },
  {
    id: "6",
    name: "數位轉型趨勢",
    type: "analysis",
    source: "topic",
    lastAnalyzed: "2024-01-10",
    volume: 980,
  },
];

export function TopicSelector({
  selectedTopics,
  onTopicChange,
  maxTopics = 5,
  minTopics = 2,
}: Readonly<TopicSelectorProps>) {
  const { t } = useTranslation("topicSelector");
  const [customKeywords, setCustomKeywords] = useState<Topic[]>([]);
  const [newKeyword, setNewKeyword] = useState("");

  const allTopics = [...analysisHistory, ...customKeywords];

  const handleTopicToggle = (topicId: string, checked: boolean) => {
    if (checked && selectedTopics.length >= maxTopics) {
      return; // 達到最大選擇數量
    }

    if (checked) {
      onTopicChange([...selectedTopics, topicId]);
    } else {
      onTopicChange(selectedTopics.filter((id) => id !== topicId));
    }
  };

  const handleAddKeyword = () => {
    if (
      newKeyword.trim() &&
      !customKeywords.find((k) => k.name === newKeyword.trim())
    ) {
      const newTopic: Topic = {
        id: `custom_${Date.now()}`,
        name: newKeyword.trim(),
        type: "keyword",
        source: "custom",
      };
      setCustomKeywords([...customKeywords, newTopic]);
      setNewKeyword("");
    }
  };

  const handleRemoveKeyword = (topicId: string) => {
    setCustomKeywords(customKeywords.filter((k) => k.id !== topicId));
    onTopicChange(selectedTopics.filter((id) => id !== topicId));
  };

  const getSourceBadge = (source: string) => {
    switch (source) {
      case "comprehensive":
        return (
          <Badge variant="secondary" className="text-xs">
            {t("sources.comprehensive")}
          </Badge>
        );
      case "topic":
        return (
          <Badge variant="outline" className="text-xs">
            {t("sources.topic")}
          </Badge>
        );
      case "custom":
        return (
          <Badge variant="default" className="text-xs">
            {t("sources.custom")}
          </Badge>
        );
      default:
        return null;
    }
  };

  const canSelectMore = selectedTopics.length < maxTopics;
  const hasMinimumSelection = selectedTopics.length >= minTopics;

  return (
    <Card className="border-primary-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <TrendingUp className="w-5 h-5 text-primary" />
          <span>{t("title")}</span>
        </CardTitle>
        <p className="text-sm text-gray-600">
          {t("description", { minTopics, maxTopics })}
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 選擇狀態 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge
              variant={hasMinimumSelection ? "default" : "destructive"}
              className="text-xs"
            >
              {t("selectedCount")} {selectedTopics.length} / {maxTopics}{" "}
              {t("maxTopics")}
            </Badge>
            {!hasMinimumSelection && (
              <span className="text-xs text-red-500">
                {t("minTopicsRequired")} {minTopics} {t("minTopicsUnit")}
              </span>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onTopicChange([])}
            disabled={selectedTopics.length === 0}
          >
            {t("clearAll")}
          </Button>
        </div>

        {/* 新增自訂關鍵字 */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Hash className="w-4 h-4 text-gray-400" />
            <span className="text-sm font-medium">{t("addCustomKeyword")}</span>
          </div>
          <div className="flex space-x-2">
            <Input
              placeholder={t("keywordPlaceholder")}
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleAddKeyword()}
              className="flex-1"
            />
            <Button
              onClick={handleAddKeyword}
              disabled={!newKeyword.trim() || !canSelectMore}
              size="sm"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 分析歷史記錄 */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-gray-400" />
            <span className="text-sm font-medium">{t("analysisHistory")}</span>
          </div>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {analysisHistory.map((topic) => (
              <div
                key={topic.id}
                className={`border rounded-lg p-3 transition-colors ${
                  selectedTopics.includes(topic.id)
                    ? "border-primary-300 bg-primary-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={selectedTopics.includes(topic.id)}
                    onCheckedChange={(checked) =>
                      handleTopicToggle(topic.id, checked as boolean)
                    }
                    disabled={
                      !selectedTopics.includes(topic.id) && !canSelectMore
                    }
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-sm">{topic.name}</h4>
                      {getSourceBadge(topic.source)}
                    </div>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>
                        {t("lastAnalyzed")}: {topic.lastAnalyzed}
                      </span>
                      {topic.volume && (
                        <span>
                          {t("volume")}: {topic.volume.toLocaleString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 自訂關鍵字列表 */}
        {customKeywords.length > 0 && (
          <div className="space-y-2">
            <span className="text-sm font-medium">{t("customKeywords")}</span>
            <div className="space-y-2">
              {customKeywords.map((keyword) => (
                <div
                  key={keyword.id}
                  className={`border rounded-lg p-3 transition-colors ${
                    selectedTopics.includes(keyword.id)
                      ? "border-primary-300 bg-primary-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={selectedTopics.includes(keyword.id)}
                      onCheckedChange={(checked) =>
                        handleTopicToggle(keyword.id, checked as boolean)
                      }
                      disabled={
                        !selectedTopics.includes(keyword.id) && !canSelectMore
                      }
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-sm">{keyword.name}</h4>
                        <div className="flex items-center space-x-2">
                          {getSourceBadge(keyword.source)}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                            onClick={() => handleRemoveKeyword(keyword.id)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 已選擇主題預覽 */}
        {selectedTopics.length > 0 && (
          <div className="border-t pt-3">
            <div className="text-xs text-gray-600 mb-2">
              {t("selectedTopics")}
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedTopics.map((topicId) => {
                const topic = allTopics.find((t) => t.id === topicId);
                return topic ? (
                  <Badge key={topicId} variant="secondary" className="text-xs">
                    {topic.name}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handleTopicToggle(topicId, false)}
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </Badge>
                ) : null;
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
