import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send } from "lucide-react";
import type { ChatInputProps } from "./types";

export function ChatInput({
  value,
  onChange,
  onSend,
  placeholder,
  disabled = false,
}: ChatInputProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !disabled) {
      onSend();
    }
  };

  return (
    <div className="border-t bg-white p-4">
      <div className="flex items-center space-x-2">
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="flex-1"
          onKeyDown={handleKeyDown}
          disabled={disabled}
        />
        <Button onClick={onSend} disabled={!value.trim() || disabled}>
          <Send className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
