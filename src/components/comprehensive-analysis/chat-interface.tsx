import { useTranslation } from "react-i18next";
import { AssistantMessage } from "./assistant-message";
import { ChatInput } from "./chat-input";
import { LoadingMessage } from "./loading-message";
import type { ChatInterfaceProps } from "./types";
import { UserMessage } from "./user-message";

export function ChatInterface({
  currentChat,
  isLoading,
  timeRange,
  onTimeRangeChange,
  currentInput,
  onInputChange,
  onSend,
}: ChatInterfaceProps) {
  const { t } = useTranslation("comprehensiveAnalysis");

  return (
    <>
      <div className="border-b bg-white p-4">
        <h2 className="font-semibold text-gray-900">{currentChat.title}</h2>
        <p className="text-sm text-gray-500">{t("aiAssistant")}</p>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {currentChat.messages.map((message) => (
          <div key={message.id}>
            {message.type === "user" ? (
              <UserMessage message={message} />
            ) : (
              <AssistantMessage
                message={message}
                timeRange={timeRange}
                onTimeRangeChange={onTimeRangeChange}
              />
            )}
          </div>
        ))}

        {isLoading && <LoadingMessage />}

        <div id="messages-end" />
      </div>

      <ChatInput
        value={currentInput}
        onChange={onInputChange}
        onSend={onSend}
        placeholder={t("continuePlaceholder")}
        disabled={isLoading}
      />
    </>
  );
}
