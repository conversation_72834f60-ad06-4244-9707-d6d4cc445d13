import {
  sampleVolumeData,
  sampleSourceData,
  sampleSentimentData,
  sampleWordData,
  sampleTopPosts,
} from "../constants";

export function useAIResponse() {
  const shouldIncludeCharts = (userMessage: string): boolean => {
    const chartKeywords = [
      "聲量",
      "趨勢",
      "分析",
      "統計",
      "數據",
      "圖表",
      "報告",
    ];
    return chartKeywords.some((keyword) => userMessage.includes(keyword));
  };

  const generateAIResponse = (userMessage: string): string => {
    // Simple response generation based on keywords
    if (userMessage.includes("聲量") || userMessage.includes("趨勢")) {
      return "根據最新數據分析，該品牌在過去一週的討論聲量呈現穩定上升趨勢，總提及次數為3,247次，較前週增長18%。主要討論平台分布：Facebook 42%、Instagram 28%、Twitter 20%、其他平台 10%。整體情緒傾向正面，正面情緒佔65%，中性25%，負面10%。\n\n以下是詳細的分析圖表：";
    }
    if (userMessage.includes("抱怨") || userMessage.includes("負面")) {
      return "分析負面評價內容，主要抱怨點集中在以下幾個方面：1. 服務品質問題（32%）2. 產品功能不符預期（28%）3. 價格偏高（22%）4. 客服響應速度（18%）。建議重點關注服務品質改善和產品功能優化。\n\n以下是相關的分析圖表：";
    }
    if (userMessage.includes("競品")) {
      return "競品分析顯示，主要競爭對手在社群媒體上的活躍度有所提升，特別是在產品宣傳和用戶互動方面。建議加強我們的社群經營策略，提高用戶參與度。\n\n以下是競品對比分析：";
    }
    return "我已經分析了相關數據，根據輿情監測結果，為您提供以下洞察和建議。如需更詳細的分析報告，請告訴我您想深入了解的特定方面。\n\n以下是分析結果的視覺化呈現：";
  };

  const getChartsForMessage = (userMessage: string) => {
    return shouldIncludeCharts(userMessage)
      ? {
          volumeTrend: sampleVolumeData,
          sourceDistribution: sampleSourceData,
          sentiment: sampleSentimentData,
          wordCloud: sampleWordData,
          topPosts: sampleTopPosts,
        }
      : undefined;
  };

  return {
    shouldIncludeCharts,
    generateAIResponse,
    getChartsForMessage,
  };
}
