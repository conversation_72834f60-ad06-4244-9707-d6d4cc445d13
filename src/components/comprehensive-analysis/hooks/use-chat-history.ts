import { useState } from "react";
import { initialChatHistory } from "../constants";
import type { ChatHistory } from "../types";

export function useChatHistory() {
  const [chatHistory, setChatHistory] =
    useState<ChatHistory[]>(initialChatHistory);
  const [currentChat, setCurrentChat] = useState<ChatHistory | null>(null);

  const selectChat = (chat: ChatHistory) => {
    setCurrentChat(chat);
  };

  const startNewChat = () => {
    setCurrentChat(null);
  };

  const updateChatHistory = (updatedChat: ChatHistory) => {
    setChatHistory((prev) =>
      prev.map((chat) => (chat.id === updatedChat.id ? updatedChat : chat)),
    );
  };

  const addNewChat = (newChat: ChatHistory) => {
    setChatHistory((prev) => [newChat, ...prev]);
    setCurrentChat(newChat);
  };

  return {
    chatHistory,
    currentChat,
    selectChat,
    startNew<PERSON>hat,
    updateChatHistory,
    addNew<PERSON>hat,
    setCurrentChat,
  };
}
