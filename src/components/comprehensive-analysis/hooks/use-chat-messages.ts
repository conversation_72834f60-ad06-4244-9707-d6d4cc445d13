import { useEffect, useRef, useState } from "react";
import type { ChatHistory, Message } from "../types";
import { useAIResponse } from "./use-ai-response";

interface UseChatMessagesProps {
  currentChat: ChatHistory | null;
  setCurrentChat: (chat: ChatHistory | null) => void;
  updateChatHistory: (chat: ChatHistory) => void;
  addNewChat: (chat: ChatHistory) => void;
}

export function useChatMessages({
  currentChat,
  setCurrentChat,
  updateChatHistory,
  addNewChat,
}: UseChatMessagesProps) {
  const [currentInput, setCurrentInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { generateAIResponse, getChartsForMessage } = useAIResponse();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentChat?.messages]);

  const handleSendMessage = async () => {
    if (!currentInput.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: currentInput,
      timestamp: new Date(),
    };

    // Create new chat if none exists
    if (!currentChat) {
      const newChat: ChatHistory = {
        id: Date.now().toString(),
        title: currentInput.slice(0, 20) + "...",
        lastMessage: currentInput,
        timestamp: new Date(),
        messages: [newMessage],
      };
      addNewChat(newChat);
    } else {
      // Add to existing chat
      const updatedChat = {
        ...currentChat,
        messages: [...currentChat.messages, newMessage],
        lastMessage: currentInput,
        timestamp: new Date(),
      };
      setCurrentChat(updatedChat);
      updateChatHistory(updatedChat);
    }

    setCurrentInput("");
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: generateAIResponse(newMessage.content),
        timestamp: new Date(),
        charts: getChartsForMessage(newMessage.content),
      };

      const finalChat = {
        ...currentChat!,
        messages: [...(currentChat?.messages || []), newMessage, aiResponse],
        lastMessage: aiResponse.content.slice(0, 30) + "...",
        timestamp: new Date(),
      };

      setCurrentChat(finalChat);
      updateChatHistory(finalChat);
      setIsLoading(false);
    }, 2000);
  };

  return {
    currentInput,
    setCurrentInput,
    isLoading,
    messagesEndRef,
    handleSendMessage,
  };
}
