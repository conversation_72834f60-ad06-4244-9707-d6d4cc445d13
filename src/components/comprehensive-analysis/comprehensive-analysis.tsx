import { useState } from "react";
import { ChatInterface } from "./chat-interface";
import { ChatSidebar } from "./chat-sidebar";
import { useChatHistory } from "./hooks/use-chat-history";
import { useChatMessages } from "./hooks/use-chat-messages";
import type { TimeRange } from "./types";
import { WelcomeScreen } from "./welcome-screen";

export default function ComprehensiveAnalysis() {
  const [timeRange, setTimeRange] = useState<TimeRange>("week");

  const {
    chatHistory,
    currentChat,
    selectChat,
    startNewChat,
    updateChatHistory,
    addNewChat,
    setCurrentChat,
  } = useChatHistory();

  const { currentInput, setCurrentInput, isLoading, handleSendMessage } =
    useChatMessages({
      currentChat,
      setCurrentChat,
      updateChatHistory,
      addNewChat,
    });

  const handlePromptClick = (prompt: string) => {
    setCurrentInput(prompt);
  };

  return (
    <div className="flex h-[calc(100vh-73px)]">
      {/* Left Sidebar - Chat History */}
      <ChatSidebar
        chatHistory={chatHistory}
        currentChat={currentChat}
        onSelectChat={selectChat}
        onNewChat={startNewChat}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {!currentChat ? (
          <WelcomeScreen
            onPromptClick={handlePromptClick}
            currentInput={currentInput}
            onInputChange={setCurrentInput}
            onSend={handleSendMessage}
          />
        ) : (
          <ChatInterface
            currentChat={currentChat}
            isLoading={isLoading}
            timeRange={timeRange}
            onTimeRangeChange={setTimeRange}
            currentInput={currentInput}
            onInputChange={setCurrentInput}
            onSend={handleSendMessage}
          />
        )}
      </div>
    </div>
  );
}
