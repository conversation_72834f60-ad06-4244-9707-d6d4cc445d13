import { User } from "lucide-react";
import type { MessageProps } from "./types";

export function UserMessage({ message }: MessageProps) {
  return (
    <div className="flex justify-end">
      <div className="flex items-start space-x-3 max-w-5xl w-full">
        <div className="flex-1">
          <div className="bg-primary text-white ml-auto max-w-2xl p-4 rounded-lg">
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
            <p className="text-xs mt-2 text-primary-100">
              {message.timestamp.toLocaleTimeString()}
            </p>
          </div>
        </div>
        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
          <User className="w-4 h-4 text-gray-600" />
        </div>
      </div>
    </div>
  );
}
