import { Bot } from "lucide-react";
import { useTranslation } from "react-i18next";

export function LoadingMessage() {
  const { t } = useTranslation("comprehensiveAnalysis");
  return (
    <div className="flex justify-start">
      <div className="flex items-start space-x-3 max-w-3xl">
        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
          <Bot className="w-4 h-4 text-primary" />
        </div>
        <div className="bg-white border border-gray-200 shadow-sm p-4 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div
              className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style={{ animationDelay: "0.1s" }}
            ></div>
            <div
              className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
              style={{ animationDelay: "0.2s" }}
            ></div>
            <span className="text-sm text-gray-500 ml-2">{t("analyzing")}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
