// Sample data for charts
export const sampleVolumeData = [
  { date: "01/15", volume: 245 },
  { date: "01/16", volume: 312 },
  { date: "01/17", volume: 189 },
  { date: "01/18", volume: 456 },
  { date: "01/19", volume: 378 },
  { date: "01/20", volume: 523 },
  { date: "01/21", volume: 467 },
];

export const sampleSourceData = [
  { name: "Facebook", value: 1247, color: "#1877F2" },
  { name: "Instagram", value: 892, color: "#E4405F" },
  { name: "Twitter", value: 634, color: "#1DA1F2" },
  { name: "PTT", value: 456, color: "#FF6600" },
  { name: "新聞媒體", value: 789, color: "#34D399" },
  { name: "其他", value: 234, color: "#9CA3AF" },
];

export const sampleSentimentData = [
  { sentiment: "正面", count: 2156, percentage: 68 },
  { sentiment: "中立", count: 697, percentage: 22 },
  { sentiment: "負面", count: 317, percentage: 10 },
];

export const sampleWordData = [
  { text: "品質", size: 45 },
  { text: "服務", size: 38 },
  { text: "價格", size: 32 },
  { text: "推薦", size: 28 },
  { text: "滿意", size: 25 },
  { text: "快速", size: 22 },
  { text: "專業", size: 20 },
  { text: "友善", size: 18 },
  { text: "便宜", size: 15 },
  { text: "優質", size: 12 },
];

export const sampleTopPosts = [
  {
    id: "1",
    content:
      "剛用了這個品牌的新產品，真的超級滿意！品質比預期還要好，客服也很專業，強烈推薦給大家！",
    author: "王小明",
    platform: "Facebook",
    publishTime: "2025-01-20 14:30",
    interactions: { likes: 1247, shares: 89, comments: 156 },
    sentiment: "positive" as const,
  },
  {
    id: "2",
    content:
      "使用心得分享：這次購買體驗整體不錯，產品功能符合需求，配送也很快速。唯一小缺點是包裝可以再精美一點。",
    author: "李美華",
    platform: "Instagram",
    publishTime: "2025-01-19 16:45",
    interactions: { likes: 892, shares: 67, comments: 134 },
    sentiment: "positive" as const,
  },
  {
    id: "3",
    content:
      "客服回應速度需要改善，等了兩天才收到回覆。產品本身還可以，但服務體驗有待加強。",
    author: "張志明",
    platform: "PTT",
    publishTime: "2025-01-18 09:15",
    interactions: { likes: 234, shares: 45, comments: 89 },
    sentiment: "negative" as const,
  },
];

export const initialChatHistory = [
  {
    id: "1",
    title: "品牌聲量分析",
    lastMessage: "過去一週品牌討論量增加了15%",
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    messages: [
      {
        id: "1",
        type: "user" as const,
        content: "過去一週品牌X的討論聲量趨勢如何？",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      },
      {
        id: "2",
        type: "assistant" as const,
        content:
          "根據分析，過去一週品牌X的討論聲量呈現上升趨勢，總計有2,847則提及，相較前週增加15%。主要討論集中在社群媒體平台，其中Facebook佔45%，Instagram佔30%，Twitter佔25%。正面情緒佔68%，中性22%，負面10%。\n\n以下是詳細的分析圖表：",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        charts: {
          volumeTrend: sampleVolumeData,
          sourceDistribution: sampleSourceData,
          sentiment: sampleSentimentData,
          wordCloud: sampleWordData,
          topPosts: sampleTopPosts,
        },
      },
    ],
  },
  {
    id: "2",
    title: "產品評價分析",
    lastMessage: "主要抱怨集中在配送速度問題",
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    messages: [
      {
        id: "1",
        type: "user" as const,
        content: "有沒有關於產品Y的主要抱怨點？",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      },
      {
        id: "2",
        type: "assistant" as const,
        content:
          "分析產品Y的負面評價，主要抱怨點包括：1. 配送速度慢（35%）2. 包裝品質問題（28%）3. 客服回應時間長（22%）4. 產品說明不清楚（15%）。建議優先改善配送流程和包裝標準。",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      },
    ],
  },
  {
    id: "3",
    title: "競品監測",
    lastMessage: "競品推出新功能引發討論",
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    messages: [],
  },
];
