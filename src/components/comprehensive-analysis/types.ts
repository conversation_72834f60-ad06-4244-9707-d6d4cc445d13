/* eslint-disable @typescript-eslint/no-explicit-any */
export interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
  charts?: {
    volumeTrend?: any;
    sourceDistribution?: any;
    sentiment?: any;
    wordCloud?: any;
    topPosts?: any;
  };
}

export interface ChatHistory {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messages: Message[];
}

export type TimeRange = "day" | "week" | "month";

export interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  placeholder: string;
  disabled?: boolean;
}

export interface ChatSidebarProps {
  chatHistory: ChatHistory[];
  currentChat: ChatHistory | null;
  onSelectChat: (chat: ChatHistory) => void;
  onNewChat: () => void;
}

export interface WelcomeScreenProps {
  onPromptClick: (prompt: string) => void;
  currentInput: string;
  onInputChange: (value: string) => void;
  onSend: () => void;
}

export interface ChatInterfaceProps {
  currentChat: ChatHistory;
  isLoading: boolean;
  timeRange: TimeRange;
  onTimeRangeChange: (range: TimeRange) => void;
  currentInput: string;
  onInputChange: (value: string) => void;
  onSend: () => void;
}

export interface MessageProps {
  message: Message;
  timeRange?: TimeRange;
  onTimeRangeChange?: (range: TimeRange) => void;
}

export interface ChartsSectionProps {
  charts: NonNullable<Message["charts"]>;
  timeRange: TimeRange;
  onTimeRangeChange: (range: TimeRange) => void;
}
