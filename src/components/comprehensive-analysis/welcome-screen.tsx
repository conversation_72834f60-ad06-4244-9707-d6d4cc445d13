import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  <PERSON>ertCircle,
  BarChart3,
  Brain,
  Send,
  Sparkles,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import type { WelcomeScreenProps } from "./types";

export function WelcomeScreen({
  onPromptClick,
  currentInput,
  onInputChange,
  onSend,
}: WelcomeScreenProps) {
  const { t } = useTranslation("comprehensiveAnalysis");

  const suggestedPrompts = [
    t("prompts.brandTrend"),
    t("prompts.productComplaints"),
    t("prompts.competitorSentiment"),
    t("prompts.activePlatforms"),
    t("prompts.negativeReviews"),
    t("prompts.productReaction"),
    t("prompts.socialSentiment"),
    t("prompts.hotTopics"),
  ];

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSend();
    }
  };

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Brain className="w-8 h-8 text-primary" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t("title")}</h1>
        <p className="text-gray-600">{t("subtitle")}</p>
      </div>

      <div className="w-full max-w-2xl">
        <div className="flex items-center space-x-2 mb-4">
          <Input
            value={currentInput}
            onChange={(e) => onInputChange(e.target.value)}
            placeholder={t("inputPlaceholder")}
            className="flex-1"
            onKeyDown={handleKeyDown}
          />
          <Button onClick={onSend} disabled={!currentInput.trim()}>
            <Send className="w-4 h-4" />
          </Button>
        </div>

        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
            <Sparkles className="w-4 h-4 mr-2" />
            {t("suggestedPrompts")}
          </h3>
          <div className="flex flex-wrap gap-2">
            {suggestedPrompts.map((prompt, index) => (
              <Badge
                key={index}
                variant="outline"
                className="cursor-pointer hover:bg-primary-50 hover:border-primary text-sm py-2 px-3"
                onClick={() => onPromptClick(prompt)}
              >
                {prompt}
              </Badge>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center space-x-3">
              <TrendingUp className="w-8 h-8 text-green-500" />
              <div>
                <h4 className="font-medium">{t("features.trendAnalysis")}</h4>
                <p className="text-sm text-gray-600">
                  {t("features.trendAnalysisDesc")}
                </p>
              </div>
            </div>
          </Card>
          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-8 h-8 text-orange-500" />
              <div>
                <h4 className="font-medium">
                  {t("features.anomalyDetection")}
                </h4>
                <p className="text-sm text-gray-600">
                  {t("features.anomalyDetectionDesc")}
                </p>
              </div>
            </div>
          </Card>
          <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-center space-x-3">
              <BarChart3 className="w-8 h-8 text-blue-500" />
              <div>
                <h4 className="font-medium">{t("features.deepInsights")}</h4>
                <p className="text-sm text-gray-600">
                  {t("features.deepInsightsDesc")}
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
