import { Sentiment<PERSON><PERSON><PERSON><PERSON> } from "@/components/charts/sentiment-bar-chart";
import { SourceDistributionChart } from "@/components/charts/source-distribution-chart";
import { TopPosts } from "@/components/charts/top-posts";
import { VolumeTrendChart } from "@/components/charts/volume-trend-chart";
import { WordCloud } from "@/components/charts/word-cloud";
import type { ChartsSectionProps } from "./types";

export function ChartsSection({
  charts,
  timeRange,
  onTimeRangeChange,
}: ChartsSectionProps) {
  return (
    <div className="mt-4 space-y-6">
      {charts.volumeTrend && (
        <VolumeTrendChart
          data={charts.volumeTrend}
          timeRange={timeRange}
          onTimeRangeChange={onTimeRangeChange}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {charts.sourceDistribution && (
          <SourceDistributionChart data={charts.sourceDistribution} />
        )}
        {charts.sentiment && <SentimentBarChart data={charts.sentiment} />}
      </div>

      {charts.wordCloud && <WordCloud words={charts.wordCloud} />}

      {charts.topPosts && <TopPosts posts={charts.topPosts} />}
    </div>
  );
}
