import { Bot } from "lucide-react";
import { ChartsSection } from "./charts-section";
import type { MessageProps } from "./types";

export function AssistantMessage({
  message,
  timeRange = "week",
  onTimeRangeChange,
}: MessageProps) {
  return (
    <div className="flex justify-start">
      <div className="flex items-start space-x-3 max-w-5xl w-full">
        <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
          <Bot className="w-4 h-4 text-primary" />
        </div>
        <div className="flex-1">
          <div className="bg-white border border-gray-200 shadow-sm p-4 rounded-lg">
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
            <p className="text-xs mt-2 text-gray-500">
              {message.timestamp.toLocaleTimeString()}
            </p>
          </div>

          {/* Charts Section */}
          {message.charts && onTimeRangeChange && (
            <ChartsSection
              charts={message.charts}
              timeRange={timeRange}
              onTimeRangeChange={onTimeRangeChange}
            />
          )}
        </div>
      </div>
    </div>
  );
}
