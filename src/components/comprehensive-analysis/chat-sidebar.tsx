import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Clock, MessageSquare, Plus, Search } from "lucide-react";
import { useTranslation } from "react-i18next";
import type { ChatSidebarProps } from "./types";

export function ChatSidebar({
  chatHistory,
  currentChat,
  onSelectChat,
  onNewChat,
}: ChatSidebarProps) {
  const { t } = useTranslation("comprehensiveAnalysis");
  const { t: common } = useTranslation("common");

  return (
    <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
      <div className="p-4 border-b">
        <Button
          onClick={onNewChat}
          className="w-full bg-primary hover:bg-primary-600"
        >
          <Plus className="w-4 h-4 mr-2" />
          {t("newChat")}
        </Button>
      </div>

      <div className="p-4 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input placeholder={common("search")} className="pl-10" />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-2">
          <h3 className="text-sm font-medium text-gray-500 mb-2 px-2">
            {t("chatHistory")}
          </h3>
          {chatHistory.map((chat) => (
            <Button
              key={chat.id}
              variant="ghost"
              className={`w-full justify-start p-3 h-auto mb-1 ${
                currentChat?.id === chat.id
                  ? "bg-primary-50 text-primary-700"
                  : ""
              }`}
              onClick={() => onSelectChat(chat)}
            >
              <div className="flex flex-col items-start w-full">
                <div className="flex items-center w-full mb-1">
                  <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" />
                  <span className="font-medium text-sm truncate">
                    {chat.title}
                  </span>
                </div>
                <p className="text-xs text-gray-500 truncate w-full text-left">
                  {chat.lastMessage}
                </p>
                <div className="flex items-center mt-1 text-xs text-gray-400">
                  <Clock className="w-3 h-3 mr-1" />
                  {chat.timestamp.toLocaleString()}
                </div>
              </div>
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
