import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import useAuthStore from "@/stores/useAuthStore";
import { Bell, Search, Settings, User } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router";

export function Header() {
  const { t } = useTranslation("header");
  // Remove showDropdown state
  const navigate = useNavigate();

  const handleLogout = () => {
    // Clear token and user info, then redirect to login
    useAuthStore.getState().clearToken();
    useAuthStore.getState().setUser(null);
    navigate("/login");
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <img
              src="/images/bonito-icon.png"
              alt="BONITO Logo"
              className="h-8 w-8"
            />
            <h1 className="text-2xl font-bold text-neutral-600">BONITO</h1>
          </div>
          <div className="hidden md:flex items-center space-x-2 bg-gray-100 rounded-full px-4 py-2">
            <span className="text-sm text-gray-600">{t("platformName")}</span>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder={t("searchPlaceholder")}
              className="pl-10 w-64 bg-gray-50 border-0"
            />
          </div>
          <Button variant="ghost" size="icon" title={t("notifications")}>
            <Bell className="w-5 h-5" />
          </Button>
          <Button variant="ghost" size="icon" title={t("settings")}>
            <Settings className="w-5 h-5" />
          </Button>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" title={t("profile")}>
                <User className="w-5 h-5" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="max-w-[160px] p-0" align="end">
              <button
                className="text-left px-2 py-2 hover:bg-gray-100 text-gray-700"
                onClick={handleLogout}
              >
                {t("logout")}
              </button>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </header>
  );
}
