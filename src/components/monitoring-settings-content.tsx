import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Settings,
  Plus,
  Edit,
  Trash2,
  Filter,
  Users,
  FileText,
  Target,
} from "lucide-react";

const keywordGroups = [
  {
    id: 1,
    name: "個案_陳汶軒",
    color: "bg-green-500",
    keywords: ["陳汶軒", "性騷擾", "#MeToo", "實工民進黨"],
    assignees: ["admin", "<PERSON>"],
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    color: "bg-pink-500",
    keywords: ["<PERSON>ne", "新年也要對化妝包大掃除", "知道該怎麼不同的功用間"],
    assignees: ["admin"],
  },
  {
    id: 3,
    name: "黃建富",
    color: "bg-teal-500",
    keywords: ["黃建富"],
    assignees: ["admin", "tra"],
  },
];

const clusters = [
  { name: "單一頻道抓取", sourceCount: "0+1", totalCount: 50 },
  {
    name: "主流新聞媒體(日報用)_20250110",
    sourceCount: "38+0",
    totalCount: 50,
  },
  {
    name: "主流新聞媒體(週報用)_20250120",
    sourceCount: "22+0",
    totalCount: 50,
  },
];

const blacklistKeywords = [
  { keyword: "洗錢", date: "2024-08-26" },
  { keyword: "打詐", date: "2024-08-26" },
  { keyword: "詐騙", date: "2024-08-26" },
  { keyword: "詐騙集團勾結", date: "2024-08-26" },
];

export function MonitoringSettingsContent() {
  const [activeTab, setActiveTab] = useState("keywords");

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-2">
          <Target className="w-6 h-6" />
          <h2 className="text-2xl font-bold">分析設定</h2>
        </div>
        <p className="text-white/90">
          設定關鍵字監測、群集管理、黑白名單過濾，打造專屬的輿情分析環境
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-4">
              <nav className="space-y-2">
                <Button
                  variant={activeTab === "keywords" ? "default" : "ghost"}
                  onClick={() => setActiveTab("keywords")}
                  className="w-full justify-start"
                >
                  <Target className="w-4 h-4 mr-2" />
                  關鍵字設定
                </Button>
                <Button
                  variant={activeTab === "clusters" ? "default" : "ghost"}
                  onClick={() => setActiveTab("clusters")}
                  className="w-full justify-start"
                >
                  <Filter className="w-4 h-4 mr-2" />
                  群集設定
                </Button>
                <Button
                  variant={activeTab === "blacklist" ? "default" : "ghost"}
                  onClick={() => setActiveTab("blacklist")}
                  className="w-full justify-start"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  黑白名單
                </Button>
                <Button
                  variant={activeTab === "advanced" ? "default" : "ghost"}
                  onClick={() => setActiveTab("advanced")}
                  className="w-full justify-start"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  進階設定
                </Button>
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Keywords Section */}
          {activeTab === "keywords" && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>關鍵字群組管理</CardTitle>
                  <p className="text-gray-600 mt-1">
                    建立關鍵字群組，設定監測範圍與負責人員
                  </p>
                </div>
                <Button className="bg-primary hover:bg-primary-600">
                  <Plus className="w-4 h-4 mr-2" />
                  新增群組
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {keywordGroups.map((group) => (
                  <div
                    key={group.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-4 h-4 rounded-full ${group.color}`}
                        ></div>
                        <h4 className="font-semibold">{group.name}</h4>
                        <div className="flex items-center space-x-1">
                          <Users className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {group.assignees.join(", ")}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {group.keywords.map((keyword, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs"
                        >
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Clusters Section */}
          {activeTab === "clusters" && (
            <Card>
              <CardHeader>
                <CardTitle>群集管理</CardTitle>
                <p className="text-gray-600">
                  管理資料來源群集，包含來源群集、排除群集設定
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {clusters.map((cluster, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center p-4 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <h4 className="font-medium">{cluster.name}</h4>
                      <p className="text-sm text-gray-600">
                        ({cluster.sourceCount})/{cluster.totalCount}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Blacklist Section */}
          {activeTab === "blacklist" && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>黑白名單管理</CardTitle>
                  <p className="text-gray-600 mt-1">
                    設定過濾關鍵字，提升分析結果準確度
                  </p>
                </div>
                <Button className="bg-primary hover:bg-primary-600">
                  <Plus className="w-4 h-4 mr-2" />
                  新增關鍵字
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {blacklistKeywords.map((item, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-3 hover:bg-gray-50 rounded"
                    >
                      <div>
                        <span className="font-medium">{item.keyword}</span>
                        <p className="text-xs text-gray-500">{item.date}</p>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Advanced Section */}
          {activeTab === "advanced" && (
            <Card>
              <CardHeader>
                <CardTitle>進階設定</CardTitle>
                <p className="text-gray-600">
                  文章清理、資料處理等進階功能設定
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">文章清理設定</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>自動清理過期文章</span>
                        <Button variant="outline" size="sm">
                          啟用
                        </Button>
                      </div>
                      <div className="flex justify-between">
                        <span>重複文章合併</span>
                        <Button variant="outline" size="sm">
                          啟用
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">資料處理設定</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>即時資料更新</span>
                        <Button variant="outline" size="sm">
                          啟用
                        </Button>
                      </div>
                      <div className="flex justify-between">
                        <span>情感分析處理</span>
                        <Button variant="outline" size="sm">
                          啟用
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
