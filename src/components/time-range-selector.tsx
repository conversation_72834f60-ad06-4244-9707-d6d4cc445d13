import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Calendar } from "lucide-react";
import type { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";

interface TimeRangeSelectorProps {
  period1: DateRange | undefined;
  period2: DateRange | undefined;
  onPeriod1Change: (range: DateRange | undefined) => void;
  onPeriod2Change: (range: DateRange | undefined) => void;
}

export function TimeRangeSelector({
  period1,
  period2,
  onPeriod1Change,
  onPeriod2Change,
}: Readonly<TimeRangeSelectorProps>) {
  const { t } = useTranslation("timeRangeSelector");

  type QuickRangeLabelKey =
    | "quickSelect.today"
    | "quickSelect.last7Days"
    | "quickSelect.last15Days"
    | "quickSelect.last30Days"
    | "quickSelect.last12Months";

  interface QuickRange {
    labelKey: QuickRangeLabelKey;
    days: number;
  }

  const quickRanges: QuickRange[] = [
    { labelKey: "quickSelect.today", days: 0 },
    { labelKey: "quickSelect.last7Days", days: 7 },
    { labelKey: "quickSelect.last15Days", days: 15 },
    { labelKey: "quickSelect.last30Days", days: 30 },
    { labelKey: "quickSelect.last12Months", days: 365 },
  ];
  const getDateRange = (days: number): DateRange => {
    const end = new Date();
    const start = new Date();

    if (days === 0) {
      // 今日
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
    } else {
      start.setDate(end.getDate() - days + 1);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
    }

    return { from: start, to: end };
  };

  const handleQuickRange = (days: number) => {
    const range = getDateRange(days);
    onPeriod1Change(range);
    onPeriod2Change(undefined); // 清空第二個區間
  };

  return (
    <Card className="border-primary-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-lg">
          <Calendar className="w-5 h-5 text-primary" />
          <span>{t("title")}</span>
        </CardTitle>
        <p className="text-sm text-gray-600">{t("description")}</p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 快速選擇按鈕 */}
        <div className="space-y-3">
          <div className="text-sm font-medium">{t("quickSelect.title")}</div>
          <p className="text-xs text-gray-500">
            {t("quickSelect.description")}
          </p>
          <div className="flex flex-wrap gap-2">
            {quickRanges.map((range) => (
              <Button
                key={range.labelKey}
                variant="outline"
                size="sm"
                onClick={() => handleQuickRange(range.days)}
              >
                {t(range.labelKey)}
              </Button>
            ))}
          </div>
        </div>

        {/* 分隔線 */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              {t("manualSelect")}
            </span>
          </div>
        </div>

        {/* 時間區間 1 */}
        <div className="space-y-3">
          <div className="text-sm font-medium">{t("period1.title")}</div>
          <DateRangePicker value={period1} onChange={onPeriod1Change} />
          {period1 && (
            <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
              {t("period1.selected", {
                from: period1.from?.toLocaleDateString("zh-TW") || "",
                to: period1.to?.toLocaleDateString("zh-TW") || "",
              })}
            </div>
          )}
        </div>

        {/* 時間區間 2 */}
        <div className="space-y-3">
          <div className="text-sm font-medium">
            {t("period2.title")}{" "}
            <span className="text-xs text-gray-500">
              {t("period2.optional")}
            </span>
          </div>
          <DateRangePicker value={period2} onChange={onPeriod2Change} />
          {period2 && (
            <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
              {t("period2.selected", {
                from: period2.from?.toLocaleDateString("zh-TW") || "",
                to: period2.to?.toLocaleDateString("zh-TW") || "",
              })}
            </div>
          )}
        </div>

        {/* 分析模式提示 */}
        <div className="bg-gray-50 p-3 rounded-lg">
          <div className="text-xs text-gray-600">
            {period1 && !period2 && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>{t("analysisMode.single")}</span>
              </div>
            )}
            {period1 && period2 && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>{t("analysisMode.comparison")}</span>
              </div>
            )}
            {!period1 && (
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>{t("analysisMode.selectPrompt")}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
