import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Eye, MessageSquare, Plus } from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";
import { AnalysisCard } from "./analysis-card";
import { TopicSelector, type AnalysisRecord } from "./topic-selector";

interface RecentAnalysisSectionProps {
  analysisRecords: AnalysisRecord[];
  selectedTopics: string[];
  dropdownOpen: boolean;
  onDropdownToggle: () => void;
  onTopicToggle: (id: string) => void;
  onSelectAll: () => void;
  onViewAnalysis: (e: React.MouseEvent, query: string) => void;
}

export function RecentAnalysisSection({
  analysisRecords,
  selectedTopics,
  dropdownOpen,
  onDropdownToggle,
  onTopicToggle,
  onSelectAll,
  onViewAnalysis,
}: RecentAnalysisSectionProps) {
  const { t } = useTranslation("dashboard");

  const filteredRecords = analysisRecords.filter((record) =>
    selectedTopics.includes(record.id),
  );

  const handleNewAnalysis = () => {
    window.location.href = "/topic-analysis";
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-xl flex items-center space-x-2">
            <Eye className="w-5 h-5" />
            <span>{t("recentAnalysis")}</span>
          </CardTitle>
          <p className="text-gray-600 mt-1">{t("analysisHistory")}</p>
        </div>

        <TopicSelector
          analysisRecords={analysisRecords}
          selectedTopics={selectedTopics}
          dropdownOpen={dropdownOpen}
          onDropdownToggle={onDropdownToggle}
          onTopicToggle={onTopicToggle}
          onSelectAll={onSelectAll}
        />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {filteredRecords.map((record) => (
            <AnalysisCard
              key={record.id}
              record={record}
              onViewAnalysis={onViewAnalysis}
            />
          ))}
          {selectedTopics.length === 0 && (
            <div className="col-span-full py-12 text-center text-gray-500">
              <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">{t("noTopicsSelected")}</p>
              <p className="text-sm">{t("noTopicsDescription")}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={handleNewAnalysis}
              >
                <Plus className="w-4 h-4 mr-2" />
                {t("newAnalysis")}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
