import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageCircle, Newspaper, Share2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { DataSourceCard } from "./data-source-card";

const dataSourceCards = [
  {
    icon: MessageCircle,
    titleKey: "forumDiscussion",
    subtitleKey: "forumPlatforms",
    count: "12,450",
    color: "bg-primary-400",
    lightColor: "bg-primary-50",
  },
  {
    icon: Share2,
    titleKey: "socialMedia",
    subtitleKey: "socialPlatforms",
    count: "189,320",
    color: "bg-secondary-400",
    lightColor: "bg-secondary-50",
  },
  {
    icon: Newspaper,
    titleKey: "newsMedia",
    subtitleKey: "newsPlatforms",
    count: "23,450",
    color: "bg-secondary-500",
    lightColor: "bg-secondary-50",
  },
];

export function DataSourcesSection() {
  const { t } = useTranslation("dashboard");

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-xl">{t("dataSourcesTitle")}</CardTitle>
          <p className="text-gray-600 mt-1">
            {t("dataSourcesSubtitle")}:{" "}
            <span className="font-semibold text-primary">295,490</span>{" "}
            {t("records")}
          </p>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {dataSourceCards.map((source, index) => (
            <DataSourceCard
              key={index}
              icon={source.icon}
              title={t(source.titleKey)}
              subtitle={t(source.subtitleKey)}
              count={source.count}
              color={source.color}
              lightColor={source.lightColor}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
