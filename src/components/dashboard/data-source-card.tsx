import { Card, CardContent } from "@/components/ui/card";
import type { LucideIcon } from "lucide-react";

interface DataSourceCardProps {
  icon: LucideIcon;
  title: string;
  subtitle: string;
  count: string;
  color: string;
  lightColor: string;
}

export function DataSourceCard({
  icon: Icon,
  title,
  subtitle,
  count,
  color,
  lightColor,
}: DataSourceCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className={`p-3 rounded-xl ${lightColor}`}>
            <Icon className={`w-6 h-6 ${color} text-white`} />
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-neutral-600">{count}</div>
          </div>
        </div>
        <div>
          <h3 className="font-semibold text-neutral-600 mb-1">{title}</h3>
          <p className="text-sm text-gray-500">{subtitle}</p>
        </div>
      </CardContent>
    </Card>
  );
}
