import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Bar<PERSON>hart3, <PERSON>own, <PERSON>h, MessageSquare, Smile } from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";
import type { AnalysisRecord } from "./topic-selector";

interface AnalysisCardProps {
  record: AnalysisRecord;
  onViewAnalysis: (e: React.MouseEvent, query: string) => void;
}

export function AnalysisCard({ record, onViewAnalysis }: AnalysisCardProps) {
  const { t } = useTranslation("dashboard");

  const handleCardClick = () => {
    window.location.href = `/topic-analysis?query=${encodeURIComponent(record.query)}&showResults=true`;
  };

  return (
    <Card
      className="hover:shadow-lg transition-all cursor-pointer border-l-4 border-l-primary"
      onClick={handleCardClick}
    >
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="font-semibold text-lg text-gray-900">
              {record.query}
            </h3>
            <p className="text-xs text-gray-500">{record.dateRange}</p>
            <p className="text-xs text-gray-500">
              {t("lastUpdate")}: {record.lastUpdate}
            </p>
          </div>
          <Badge variant="outline" className="text-xs">
            {record.resultCount.toLocaleString()} {t("recordsUnit")}
          </Badge>
        </div>

        {/* Overview */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium mb-2 flex items-center">
            <BarChart3 className="w-4 h-4 mr-1" />
            {t("overview")}
          </h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-600">{t("totalVolume")}：</span>
              <span className="font-medium">
                {record.overview.totalVolume.toLocaleString()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">{t("goodwillScore")}：</span>
              <span className="font-medium">
                {record.sentiment.goodwillScore}
              </span>
            </div>
            <div className="col-span-2">
              <span className="text-gray-600">{t("mainSource")}：</span>
              <span className="font-medium">
                {record.overview.mainSource} ({record.overview.sourcePercentage}
                %)
              </span>
            </div>
          </div>
        </div>

        {/* Sentiment Analysis */}
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2 flex items-center">
            <Smile className="w-4 h-4 mr-1" />
            {t("sentiment")}
          </h4>
          <div className="flex space-x-3 mb-2">
            <div className="flex items-center space-x-1">
              <Smile className="w-3 h-3 text-green-500" />
              <span className="text-xs font-medium text-green-600">
                {record.sentiment.positive}%
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Meh className="w-3 h-3 text-gray-500" />
              <span className="text-xs font-medium text-gray-600">
                {record.sentiment.neutral}%
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Frown className="w-3 h-3 text-red-500" />
              <span className="text-xs font-medium text-red-600">
                {record.sentiment.negative}%
              </span>
            </div>
          </div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div className="h-full flex">
              <div
                className="bg-green-500"
                style={{ width: `${record.sentiment.positive}%` }}
              ></div>
              <div
                className="bg-gray-400"
                style={{ width: `${record.sentiment.neutral}%` }}
              ></div>
              <div
                className="bg-red-500"
                style={{ width: `${record.sentiment.negative}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Word Cloud */}
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2 flex items-center">
            <MessageSquare className="w-4 h-4 mr-1" />
            {t("hotKeywords")}
          </h4>
          <div
            className="flex flex-wrap gap-1 overflow-hidden"
            style={{ maxHeight: "24px" }}
          >
            {record.wordCloud.slice(0, 4).map((word, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-primary-50 text-primary-700 rounded-full text-xs whitespace-nowrap"
                style={{ fontSize: "10px" }}
              >
                {word.text}
              </span>
            ))}
            {record.wordCloud.length > 4 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs whitespace-nowrap">
                +{record.wordCloud.length - 4}
              </span>
            )}
          </div>
        </div>

        {/* Action Button */}
        <div className="pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={(e) => onViewAnalysis(e, record.query)}
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            {t("viewFullAnalysis")}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
