import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

export interface AnalysisRecord {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
  status: string;
  summary: string;
  dateRange: string;
  sentiment: {
    positive: number;
    neutral: number;
    negative: number;
    goodwillScore: number;
  };
  overview: {
    totalVolume: number;
    monthlyChange: number;
    mainSource: string;
    sourcePercentage: number;
  };
  wordCloud: Array<{ text: string; size: number }>;
  lastUpdate: string;
}

interface TopicSelectorProps {
  analysisRecords: AnalysisRecord[];
  selectedTopics: string[];
  dropdownOpen: boolean;
  onDropdownToggle: () => void;
  onTopicToggle: (id: string) => void;
  onSelectAll: () => void;
}

export function TopicSelector({
  analysisRecords,
  selectedTopics,
  dropdownOpen,
  onDropdownToggle,
  onTopicToggle,
  onSelectAll,
}: TopicSelectorProps) {
  const { t: common } = useTranslation("common");
  const { t } = useTranslation("dashboard");

  const handleNewAnalysis = () => {
    window.location.href = "/topic-analysis";
  };

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        className="border-primary text-primary hover:bg-primary-50"
        onClick={onDropdownToggle}
      >
        {common("settings")}
        <ChevronDown className="w-4 h-4 ml-2" />
      </Button>

      {dropdownOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1">
            <div className="px-4 py-2 text-sm font-medium">
              {common("select")}
            </div>
            <hr className="my-1" />

            {/* Select All/Deselect All */}
            <div
              className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 flex items-center"
              onClick={onSelectAll}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => e.key === "Enter" && onSelectAll()}
            >
              <Checkbox
                checked={selectedTopics.length === analysisRecords.length}
                className="mr-2"
              />
              {selectedTopics.length === analysisRecords.length
                ? common("deselectAll")
                : common("selectAll")}
            </div>

            <hr className="my-1" />

            {/* Topic List */}
            {analysisRecords.map((record) => (
              <div
                key={record.id}
                className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 flex items-center justify-between"
                onClick={() => onTopicToggle(record.id)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => e.key === "Enter" && onTopicToggle(record.id)}
              >
                <div className="flex items-center">
                  <Checkbox
                    checked={selectedTopics.includes(record.id)}
                    className="mr-2"
                  />
                  <span>{record.query}</span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {record.resultCount.toLocaleString()}
                </Badge>
              </div>
            ))}

            <hr className="my-1" />

            {/* Add New Analysis */}
            <div
              className="px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 flex items-center"
              onClick={handleNewAnalysis}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => e.key === "Enter" && handleNewAnalysis()}
            >
              <Plus className="w-4 h-4 mr-2" />
              <span>{t("newAnalysis")}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
