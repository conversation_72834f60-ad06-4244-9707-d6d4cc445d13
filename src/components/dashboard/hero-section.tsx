import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Play, TrendingUp } from "lucide-react";
import { useTranslation } from "react-i18next";

export function HeroSection() {
  const { t } = useTranslation("dashboard");

  const handleStartAnalysis = () => {
    window.location.href = "/comprehensive-analysis";
  };

  return (
    <Card className="bg-gradient-to-r from-secondary-400 to-primary text-white border-0">
      <CardContent className="p-8">
        <div className="flex items-center justify-between">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-6 h-6" />
              <h2 className="text-2xl font-bold">{t("title")}</h2>
            </div>
            <p className="text-white/90 max-w-md">{t("subtitle")}</p>
            <Button
              className="bg-white text-primary hover:bg-gray-100"
              onClick={handleStartAnalysis}
            >
              <Play className="w-4 h-4 mr-2" />
              {t("startAnalysis")}
            </Button>
          </div>
          <div className="hidden lg:block">
            <div className="w-48 h-32 bg-white/10 rounded-xl flex items-center justify-center">
              <TrendingUp className="w-16 h-16 text-white/60" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
