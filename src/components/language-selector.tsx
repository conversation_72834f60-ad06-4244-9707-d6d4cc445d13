import i18next from "i18next";
import { Globe } from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";

export function LanguageSelector() {
  const { t } = useTranslation("common");

  const locales = ["en-US", "zh-TW"];

  const handleLocaleChange = (value: string) => {
    if (value === "en-US" || value === "zh-TW") {
      i18next.changeLanguage(value);
    }
  };

  const getLanguageName = (locale: string) => {
    switch (locale) {
      case "en-US":
        return t("english");
      case "zh-TW":
        return t("traditionalChinese");
      default:
        return locale;
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Globe className="w-4 h-4 text-gray-500" />
      <Select value={i18next.language} onValueChange={handleLocaleChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder={t("language")} />
        </SelectTrigger>
        <SelectContent>
          {locales.map((locale) => (
            <SelectItem key={locale} value={locale}>
              {getLanguageName(locale)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
