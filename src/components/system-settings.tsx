import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Settings,
  User,
  Shield,
  Mail,
  Calendar,
  Users,
  Plus,
  Edit,
  Trash2,
  Target,
  Filter,
  FileText,
  Cog,
} from "lucide-react";
import { MonitoringSettingsContent } from "@/components/monitoring-settings-content";

const users = [
  {
    username: "admin",
    role: "系統管理者",
    email: "<EMAIL>",
    roleColor: "bg-red-500",
    canDelete: false,
  },
  {
    username: "trs",
    role: "管理者",
    email: "<EMAIL>",
    roleColor: "bg-blue-500",
    canDelete: true,
  },
  {
    username: "wavenet01",
    role: "管理者",
    email: "<EMAIL>",
    roleColor: "bg-blue-500",
    canDelete: true,
  },
  {
    username: "<PERSON><PERSON>",
    role: "管理者",
    email: "<EMAIL>",
    roleColor: "bg-blue-500",
    canDelete: true,
  },
  {
    username: "wavebsd",
    role: "管理者",
    email: "<EMAIL>",
    roleColor: "bg-blue-500",
    canDelete: true,
  },
];

export function SystemSettings() {
  const [activeSection, setActiveSection] = useState("account");
  const [filterSettings, setFilterSettings] = useState({
    lowRelevance: false,
    advertising: false,
    lowContent: false,
    foreignLanguage: false,
  });

  return (
    <div className="space-y-6">
      {/* Navigation */}
      <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm">
        <Button
          variant={activeSection === "account" ? "default" : "ghost"}
          onClick={() => setActiveSection("account")}
          className="flex-1 justify-start space-x-2"
        >
          <User className="w-4 h-4" />
          <span>帳號設定</span>
        </Button>
        <Button
          variant={activeSection === "monitoring" ? "default" : "ghost"}
          onClick={() => setActiveSection("monitoring")}
          className="flex-1 justify-start space-x-2"
        >
          <Target className="w-4 h-4" />
          <span>分析設定</span>
        </Button>
      </div>

      {/* Account Settings */}
      {activeSection === "account" && (
        <div className="space-y-6">
          {/* Profile Section */}
          <Card className="overflow-hidden">
            <div className="bg-gradient-to-r from-primary-400 to-secondary-400 h-32 relative">
              <div className="absolute -bottom-16 left-8">
                <Avatar className="w-32 h-32 border-4 border-white shadow-lg">
                  <AvatarImage src="/placeholder.svg?height=128&width=128" />
                  <AvatarFallback className="text-2xl bg-white text-primary">
                    <User className="w-12 h-12" />
                  </AvatarFallback>
                </Avatar>
              </div>
            </div>
            <CardContent className="pt-20 pb-6">
              <div className="flex justify-between items-start">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <h2 className="text-2xl font-bold text-gray-900">
                      wavenet / admin
                    </h2>
                    <Badge className="bg-red-500 hover:bg-red-600">
                      <Shield className="w-3 h-3 mr-1" />
                      系統管理者
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Cog className="w-4 h-4" />
                      <span>主權限：16/16</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Mail className="w-4 h-4" />
                      <span><EMAIL></span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>帳號到期日：2025/08/06</span>
                    </div>
                  </div>
                </div>
                <Button variant="outline">
                  <Edit className="w-4 h-4 mr-2" />
                  編輯
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Filter Settings */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Filter className="w-5 h-5" />
                  <span>內容過濾設定</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">過濾相關度較低文章</h4>
                    <p className="text-sm text-gray-500">
                      自動排除相關度較低的內容
                    </p>
                  </div>
                  <Switch
                    checked={filterSettings.lowRelevance}
                    onCheckedChange={(checked) =>
                      setFilterSettings({
                        ...filterSettings,
                        lowRelevance: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">過濾內容較少文章</h4>
                    <p className="text-sm text-gray-500">排除內容過短的文章</p>
                  </div>
                  <Switch
                    checked={filterSettings.lowContent}
                    onCheckedChange={(checked) =>
                      setFilterSettings({
                        ...filterSettings,
                        lowContent: checked,
                      })
                    }
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>文章類型過濾</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">過濾廣告文章傾向文章</h4>
                    <p className="text-sm text-gray-500">
                      自動識別並排除廣告內容
                    </p>
                  </div>
                  <Switch
                    checked={filterSettings.advertising}
                    onCheckedChange={(checked) =>
                      setFilterSettings({
                        ...filterSettings,
                        advertising: checked,
                      })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">過濾全外文文章</h4>
                    <p className="text-sm text-gray-500">
                      排除非中文的文章內容
                    </p>
                  </div>
                  <Switch
                    checked={filterSettings.foreignLanguage}
                    onCheckedChange={(checked) =>
                      setFilterSettings({
                        ...filterSettings,
                        foreignLanguage: checked,
                      })
                    }
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Member Management */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5" />
                <div>
                  <CardTitle>成員管理</CardTitle>
                  <p className="text-sm text-gray-600 mt-1">服務成員：10/10</p>
                </div>
              </div>
              <Button className="bg-primary hover:bg-primary-600">
                <Plus className="w-4 h-4 mr-2" />
                新增成員帳號(10)
              </Button>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-600">
                        使用者
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">
                        角色
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">
                        信箱
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">
                        管理權組
                      </th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="py-4 px-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="text-xs">
                                {user.username.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium">{user.username}</span>
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          <Badge className={user.roleColor}>{user.role}</Badge>
                        </td>
                        <td className="py-4 px-4 text-gray-600">
                          {user.email}
                        </td>
                        <td className="py-4 px-4">
                          <Button variant="outline" size="sm">
                            <Settings className="w-3 h-3 mr-1" />
                            權限設定
                          </Button>
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              <Edit className="w-3 h-3 mr-1" />
                              權限設定
                            </Button>
                            {user.canDelete && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3 mr-1" />
                                刪除
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Monitoring Settings */}
      {activeSection === "monitoring" && <MonitoringSettingsContent />}
    </div>
  );
}
