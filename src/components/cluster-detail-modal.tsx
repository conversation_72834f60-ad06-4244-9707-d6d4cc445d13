import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ChevronDown,
  ChevronRight,
  FileText,
  Search,
  Settings,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

interface BoardItem {
  id: string;
  name: string;
  description: string;
  memberCount?: number;
  postCount?: number;
}

interface SourceItem {
  id: string;
  name: string;
  description: string;
  boards?: BoardItem[];
  memberCount?: number;
}

interface Cluster {
  id: string;
  name: string;
  description: string;
  sources: SourceItem[];
}

interface ClusterDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  cluster: Cluster | null;
  selectedSources: string[];
  selectedBoards: { [sourceId: string]: string[] };
  onSourceChange: (sourceIds: string[]) => void;
  onBoardChange: (sourceId: string, boardIds: string[]) => void;
  onSave: () => void;
}

export function ClusterDetailModal({
  isOpen,
  onClose,
  cluster,
  selectedSources,
  selectedBoards,
  onSourceChange,
  onBoardChange,
  onSave,
}: Readonly<ClusterDetailModalProps>) {
  const { t } = useTranslation("clusterSelector.modal");
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedSources, setExpandedSources] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("sources");

  // Local state management
  const [localSelectedSources, setLocalSelectedSources] = useState<string[]>(
    [],
  );
  const [localSelectedBoards, setLocalSelectedBoards] = useState<{
    [sourceId: string]: string[];
  }>({});

  // Initialize local state when modal opens
  useEffect(() => {
    if (isOpen) {
      setLocalSelectedSources([...selectedSources]);
      setLocalSelectedBoards({ ...selectedBoards });
      setSearchTerm("");
      setExpandedSources([]);
    }
  }, [isOpen, selectedSources, selectedBoards]);

  if (!cluster) return null;

  const filteredSources = cluster.sources.filter(
    (source) =>
      source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      source.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      source.boards?.some((board) =>
        board.name.toLowerCase().includes(searchTerm.toLowerCase()),
      ),
  );

  const handleSourceToggle = (sourceId: string, checked: boolean) => {
    let newSelectedSources: string[];
    if (checked) {
      newSelectedSources = [...localSelectedSources, sourceId];
    } else {
      newSelectedSources = localSelectedSources.filter((id) => id !== sourceId);
      // Clear all board selections for this source
      const newSelectedBoards = { ...localSelectedBoards };
      delete newSelectedBoards[sourceId];
      setLocalSelectedBoards(newSelectedBoards);
    }
    setLocalSelectedSources(newSelectedSources);
  };

  const handleBoardToggle = (
    sourceId: string,
    boardId: string,
    checked: boolean,
  ) => {
    const currentBoards = localSelectedBoards[sourceId] || [];
    let newBoards: string[];

    if (checked) {
      newBoards = [...currentBoards, boardId];
    } else {
      newBoards = currentBoards.filter((id) => id !== boardId);
    }

    setLocalSelectedBoards({
      ...localSelectedBoards,
      [sourceId]: newBoards,
    });
  };

  const handleSelectAllSources = () => {
    const allSourceIds = cluster.sources.map((source) => source.id);
    setLocalSelectedSources(allSourceIds);
  };

  const handleClearAllSources = () => {
    setLocalSelectedSources([]);
    setLocalSelectedBoards({});
  };

  const handleSelectAllBoards = (sourceId: string) => {
    const source = cluster.sources.find((s) => s.id === sourceId);
    if (source?.boards) {
      const allBoardIds = source.boards.map((board) => board.id);
      setLocalSelectedBoards({
        ...localSelectedBoards,
        [sourceId]: allBoardIds,
      });
    }
  };

  const handleClearAllBoards = (sourceId: string) => {
    const newSelectedBoards = { ...localSelectedBoards };
    delete newSelectedBoards[sourceId];
    setLocalSelectedBoards(newSelectedBoards);
  };

  const toggleSourceExpansion = (sourceId: string) => {
    setExpandedSources((prev) =>
      prev.includes(sourceId)
        ? prev.filter((id) => id !== sourceId)
        : [...prev, sourceId],
    );
  };

  const handleSaveAndClose = () => {
    // Save to parent component
    onSourceChange(localSelectedSources);
    Object.entries(localSelectedBoards).forEach(([sourceId, boardIds]) => {
      onBoardChange(sourceId, boardIds);
    });
    onSave();
    onClose();
  };

  const handleCancel = () => {
    // Reset local state
    setLocalSelectedSources([...selectedSources]);
    setLocalSelectedBoards({ ...selectedBoards });
    onClose();
  };

  const totalSelectedBoards = Object.values(localSelectedBoards).reduce(
    (sum, boards) => sum + boards.length,
    0,
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <span>
              {t("title")}
              {cluster.name}
            </span>
            <Badge variant="outline" className="ml-2">
              {localSelectedSources.length} {t("sourcesTab")} ·{" "}
              {totalSelectedBoards} {t("selectedBoards")}
            </Badge>
          </DialogTitle>
          <p className="text-sm text-gray-600">{cluster.description}</p>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="sources">
                {t("sourcesTab")} ({localSelectedSources.length})
              </TabsTrigger>
              <TabsTrigger value="boards">
                {t("boardsTab")} ({totalSelectedBoards})
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden">
              <TabsContent
                value="sources"
                className="h-full flex flex-col mt-4"
              >
                <div className="space-y-4 flex-1 overflow-hidden">
                  {/* Search and actions */}
                  <div className="flex items-center space-x-2">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        placeholder={t("searchSources")}
                        className="pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSelectAllSources}
                    >
                      {t("selectAllSources")}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearAllSources}
                    >
                      {t("clearAllSources")}
                    </Button>
                  </div>

                  {/* Sources list */}
                  <div className="flex-1 overflow-y-auto space-y-3">
                    {filteredSources.map((source) => (
                      <div
                        key={source.id}
                        className={`border rounded-lg p-4 transition-colors cursor-pointer ${
                          localSelectedSources.includes(source.id)
                            ? "border-primary-300 bg-primary-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() =>
                          handleSourceToggle(
                            source.id,
                            !localSelectedSources.includes(source.id),
                          )
                        }
                      >
                        <div className="flex items-start space-x-3">
                          <Checkbox
                            checked={localSelectedSources.includes(source.id)}
                            onCheckedChange={(checked) =>
                              handleSourceToggle(source.id, checked as boolean)
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-lg">
                                {source.name}
                              </h4>
                              {source.memberCount && (
                                <div className="flex items-center text-sm text-gray-500">
                                  <Users className="w-4 h-4 mr-1" />
                                  {source.memberCount.toLocaleString()}{" "}
                                  {t("memberCount")}
                                </div>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mb-3">
                              {source.description}
                            </p>

                            {source.boards && source.boards.length > 0 && (
                              <div className="text-sm text-gray-500">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span>
                                    {t("boardCount")} {source.boards.length}{" "}
                                    {t("boardsUnit")}
                                  </span>
                                  {localSelectedBoards[source.id] &&
                                    localSelectedBoards[source.id].length >
                                      0 && (
                                      <Badge
                                        variant="secondary"
                                        className="text-xs"
                                      >
                                        {t("selectedBoardsCount")}{" "}
                                        {localSelectedBoards[source.id].length}{" "}
                                        {t("moreBoards")}
                                      </Badge>
                                    )}
                                </div>
                                <div className="flex flex-wrap gap-1">
                                  {source.boards.slice(0, 5).map((board) => (
                                    <Badge
                                      key={board.id}
                                      variant={
                                        localSelectedBoards[
                                          source.id
                                        ]?.includes(board.id)
                                          ? "default"
                                          : "outline"
                                      }
                                      className="text-xs"
                                    >
                                      {board.name}
                                    </Badge>
                                  ))}
                                  {source.boards.length > 5 && (
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      +{source.boards.length - 5}{" "}
                                      {t("moreBoards")}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="boards" className="h-full flex flex-col mt-4">
                <div className="space-y-4 flex-1 overflow-hidden">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder={t("searchBoards")}
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  {/* Boards list */}
                  <div className="flex-1 overflow-y-auto space-y-4">
                    {filteredSources
                      .filter(
                        (source) =>
                          localSelectedSources.includes(source.id) &&
                          source.boards,
                      )
                      .map((source) => (
                        <div key={source.id} className="border rounded-lg">
                          <div className="p-3 bg-gray-50 border-b">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() =>
                                    toggleSourceExpansion(source.id)
                                  }
                                >
                                  {expandedSources.includes(source.id) ? (
                                    <ChevronDown className="w-4 h-4" />
                                  ) : (
                                    <ChevronRight className="w-4 h-4" />
                                  )}
                                </Button>
                                <h4 className="font-medium">{source.name}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {localSelectedBoards[source.id]?.length || 0}{" "}
                                  / {source.boards?.length || 0}
                                </Badge>
                              </div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleSelectAllBoards(source.id)
                                  }
                                >
                                  {t("selectAllBoards")}
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() =>
                                    handleClearAllBoards(source.id)
                                  }
                                >
                                  {t("clearAllBoards")}
                                </Button>
                              </div>
                            </div>
                          </div>

                          {(expandedSources.includes(source.id) ||
                            searchTerm) &&
                            source.boards && (
                              <div className="p-3 space-y-2">
                                {source.boards
                                  .filter((board) =>
                                    searchTerm
                                      ? board.name
                                          .toLowerCase()
                                          .includes(searchTerm.toLowerCase()) ||
                                        board.description
                                          .toLowerCase()
                                          .includes(searchTerm.toLowerCase())
                                      : true,
                                  )
                                  .map((board) => (
                                    <div
                                      key={board.id}
                                      className={`flex items-start space-x-3 p-3 rounded border transition-colors cursor-pointer ${
                                        localSelectedBoards[
                                          source.id
                                        ]?.includes(board.id)
                                          ? "border-primary-300 bg-primary-50"
                                          : "border-gray-200 hover:border-gray-300"
                                      }`}
                                      onClick={() =>
                                        handleBoardToggle(
                                          source.id,
                                          board.id,
                                          !localSelectedBoards[
                                            source.id
                                          ]?.includes(board.id),
                                        )
                                      }
                                    >
                                      <Checkbox
                                        checked={
                                          localSelectedBoards[
                                            source.id
                                          ]?.includes(board.id) || false
                                        }
                                        onCheckedChange={(checked) =>
                                          handleBoardToggle(
                                            source.id,
                                            board.id,
                                            checked as boolean,
                                          )
                                        }
                                        onClick={(e) => e.stopPropagation()}
                                      />
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-1">
                                          <h5 className="font-medium text-sm">
                                            {board.name}
                                          </h5>
                                          <div className="flex items-center space-x-3 text-xs text-gray-500">
                                            {board.memberCount && (
                                              <div className="flex items-center">
                                                <Users className="w-3 h-3 mr-1" />
                                                {(
                                                  board.memberCount / 1000
                                                ).toFixed(0)}
                                                k
                                              </div>
                                            )}
                                            {board.postCount && (
                                              <div className="flex items-center">
                                                <FileText className="w-3 h-3 mr-1" />
                                                {(
                                                  board.postCount / 1000
                                                ).toFixed(0)}
                                                k
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                        <p className="text-xs text-gray-600">
                                          {board.description}
                                        </p>
                                      </div>
                                    </div>
                                  ))}
                              </div>
                            )}
                        </div>
                      ))}

                    {filteredSources.filter(
                      (source) =>
                        localSelectedSources.includes(source.id) &&
                        source.boards,
                    ).length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">{t("noSourcesSelected")}</p>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <div className="text-sm text-gray-600">
            {t("selectedSources")}
            {localSelectedSources.length} {t("sourcesTab")}，
            {totalSelectedBoards} {t("selectedBoards")}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleCancel}>
              {t("cancel")}
            </Button>
            <Button
              onClick={handleSaveAndClose}
              disabled={localSelectedSources.length === 0}
            >
              {t("save")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
