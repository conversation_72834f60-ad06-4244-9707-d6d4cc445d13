import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Download,
  Edit,
  FileText,
  Filter,
  Plus,
  Settings,
  Target,
  Trash2,
  Upload,
  Users,
} from "lucide-react";
import { useState } from "react";

const keywordGroups = [
  {
    id: 1,
    name: "個案_陳汶軒",
    color: "bg-green-500",
    keywords: [
      "陳汶軒",
      "性騷擾",
      "#MeToo",
      "實工民進黨",
      "陳汶軒",
      "性騷擾",
      "#MeToo",
      "實工民進黨",
    ],
    assignees: ["admin", "Sabrina"],
  },
  {
    id: 2,
    name: "Demi",
    color: "bg-pink-500",
    keywords: [
      "Solone",
      "新年也要對化妝包大掃除",
      "知道該怎麼不同的功用間",
      "新年心願印刷即將有紅潮好氣色",
    ],
    assignees: ["admin"],
  },
  {
    id: 3,
    name: "黃建富",
    color: "bg-teal-500",
    keywords: ["黃建富"],
    assignees: ["admin", "tra"],
  },
  {
    id: 4,
    name: "食藥安全",
    color: "bg-orange-500",
    keywords: [
      "Haribol小熊軟糖",
      "大血腸",
      "食品良好衛生規範準則",
      "新冠疫苗接種",
    ],
    assignees: ["admin", "bdc2", "bdc3"],
  },
];

const clusters = [
  {
    name: "單一頻道抓取",
    sourceCount: "0+1",
    totalCount: 50,
    status: "active",
  },
  {
    name: "主流新聞媒體(日報用)_20250110",
    sourceCount: "38+0",
    totalCount: 50,
    status: "active",
  },
  {
    name: "主流新聞媒體(週報用)_20250120",
    sourceCount: "22+0",
    totalCount: 50,
    status: "active",
  },
  {
    name: "主流新聞媒體排除的結果_20250314",
    sourceCount: "0+23",
    totalCount: 50,
    status: "active",
  },
  {
    name: "來源熱門用",
    sourceCount: "0+10",
    totalCount: 50,
    status: "active",
  },
];

const blacklistKeywords = [
  { keyword: "洗錢", date: "2024-08-26" },
  { keyword: "打詐", date: "2024-08-26" },
  { keyword: "詐騙", date: "2024-08-26" },
  { keyword: "詐騙集團勾結", date: "2024-08-26" },
  { keyword: "國家級詐騙", date: "2024-08-26" },
  { keyword: "抽查10家業者", date: "2024-08-26" },
  { keyword: "巴恩斯航太股份有限公司", date: "2024-08-26" },
  { keyword: "涉貪", date: "2024-09-03" },
  { keyword: "收賄肉", date: "2024-09-03" },
  { keyword: "弊案", date: "2024-09-03" },
];

export function MonitoringSettings() {
  const [activeTab, setActiveTab] = useState("keywords");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-secondary-400 to-primary text-white rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-2">
          <Settings className="w-6 h-6" />
          <h1 className="text-2xl font-bold">分析設定</h1>
        </div>
        <p className="text-white/90">
          設定關鍵字監測、群集管理、黑白名單過濾，打造專屬的輿情分析環境
        </p>
      </div>

      {/* Main Content */}
      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger
                value="keywords"
                className="flex items-center space-x-2"
              >
                <Target className="w-4 h-4" />
                <span>關鍵字設定</span>
              </TabsTrigger>
              <TabsTrigger
                value="clusters"
                className="flex items-center space-x-2"
              >
                <Filter className="w-4 h-4" />
                <span>群集設定</span>
              </TabsTrigger>
              <TabsTrigger
                value="blacklist"
                className="flex items-center space-x-2"
              >
                <FileText className="w-4 h-4" />
                <span>黑白名單</span>
              </TabsTrigger>
              <TabsTrigger
                value="advanced"
                className="flex items-center space-x-2"
              >
                <Settings className="w-4 h-4" />
                <span>進階設定</span>
              </TabsTrigger>
            </TabsList>

            {/* Keywords Tab */}
            <TabsContent value="keywords" className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold">關鍵字群組管理</h3>
                  <p className="text-gray-600">
                    建立關鍵字群組，設定監測範圍與負責人員
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Upload className="w-4 h-4 mr-2" />
                    匯入
                  </Button>
                  <Button size="sm" className="bg-primary hover:bg-primary-600">
                    <Plus className="w-4 h-4 mr-2" />
                    新增群組
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                {keywordGroups.map((group) => (
                  <Card
                    key={group.id}
                    className="border-l-4"
                    style={{
                      borderLeftColor: group.color
                        .replace("bg-", "#")
                        .replace("-500", ""),
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center space-x-3">
                          <div
                            className={`w-3 h-3 rounded-full ${group.color}`}
                          ></div>
                          <h4 className="font-semibold">{group.name}</h4>
                          <div className="flex items-center space-x-1">
                            <Users className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-600">
                              {group.assignees.join(", ")}
                            </span>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {group.keywords.slice(0, 8).map((keyword, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="text-xs"
                          >
                            {keyword}
                          </Badge>
                        ))}
                        {group.keywords.length > 8 && (
                          <Badge variant="outline" className="text-xs">
                            +{group.keywords.length - 8} 更多
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Clusters Tab */}
            <TabsContent value="clusters" className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold">群集管理</h3>
                  <p className="text-gray-600">
                    管理資料來源群集，包含來源群集、排除群集設定
                  </p>
                </div>
                <Button size="sm" className="bg-primary hover:bg-primary-600">
                  <Plus className="w-4 h-4 mr-2" />
                  新增群集
                </Button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>來源群集</span>
                      <Badge variant="secondary">群集數量：5/5</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {clusters.map((cluster, index) => (
                      <div
                        key={index}
                        className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                      >
                        <div>
                          <h4 className="font-medium text-sm">
                            {cluster.name}
                          </h4>
                          <p className="text-xs text-gray-600">
                            ({cluster.sourceCount})/{cluster.totalCount}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="ghost" size="sm">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>排除群集</span>
                      <Badge variant="secondary">排除數量：4/5</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-500">
                      <Filter className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">尚未設定排除群集</p>
                      <Button variant="outline" size="sm" className="mt-2">
                        新增排除規則
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Blacklist Tab */}
            <TabsContent value="blacklist" className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold">黑白名單管理</h3>
                  <p className="text-gray-600">
                    設定過濾關鍵字，提升分析結果準確度
                  </p>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Excel匯入
                  </Button>
                  <Button size="sm" className="bg-primary hover:bg-primary-600">
                    <Plus className="w-4 h-4 mr-2" />
                    新增
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>黑名單</span>
                      <Badge variant="destructive">
                        共 {blacklistKeywords.length} 筆排除關鍵字
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-96 overflow-y-auto">
                      {blacklistKeywords.map((item, index) => (
                        <div
                          key={index}
                          className="flex justify-between items-center p-2 hover:bg-gray-50 rounded"
                        >
                          <div>
                            <span className="font-medium">{item.keyword}</span>
                            <p className="text-xs text-gray-500">{item.date}</p>
                          </div>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>白名單</span>
                      <Badge variant="secondary">共 0 筆指定關鍵字</Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">尚未設定白名單關鍵字</p>
                      <Button variant="outline" size="sm" className="mt-2">
                        新增白名單
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold">進階設定</h3>
                <p className="text-gray-600">
                  文章清理、資料處理等進階功能設定
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>文章清理設定</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>自動清理過期���章</span>
                      <Button variant="outline" size="sm">
                        啟用
                      </Button>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>重複文章合併</span>
                      <Button variant="outline" size="sm">
                        啟用
                      </Button>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>垃圾文章過濾</span>
                      <Button variant="outline" size="sm">
                        啟用
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>資料處理設定</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>即時資料更新</span>
                      <Button variant="outline" size="sm">
                        啟用
                      </Button>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>情感分析處理</span>
                      <Button variant="outline" size="sm">
                        啟用
                      </Button>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>關鍵字自動標記</span>
                      <Button variant="outline" size="sm">
                        啟用
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
