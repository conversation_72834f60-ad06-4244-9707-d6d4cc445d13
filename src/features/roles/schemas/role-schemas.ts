export interface RoleData {
  id: number;
  name: string;
  description: string;
  permissions: string[];
  memberCount: number;
  createdAt: string;
}

export const createRoleValidationSchema = (
  t: (key: string, values?: Record<string, unknown>) => string
) => ({
  name: {
    required: t("validation.required"),
    minLength: {
      value: 3,
      message: t("validation.minLength", { count: 3 }),
    },
    maxLength: {
      value: 50,
      message: t("validation.maxLength", { count: 50 }),
    },
  },
  description: {
    maxLength: {
      value: 200,
      message: t("validation.maxLength", { count: 200 }),
    },
  },
  permissions: {
    required: t("validation.required"),
  },
});
