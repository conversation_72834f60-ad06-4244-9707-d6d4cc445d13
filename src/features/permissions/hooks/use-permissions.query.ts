import { useQuery } from "@tanstack/react-query";
import { useAxios } from "../../../hooks/use-axios";

type Permission = string;

export function usePermissionsQuery() {
  const { axiosInstance } = useAxios();

  return useQuery<Permission[]>({
    queryKey: ["permissions"],
    queryFn: async () => {
      const response = await axiosInstance.get("/permissions");
      return response.data;
    },
  });
}
