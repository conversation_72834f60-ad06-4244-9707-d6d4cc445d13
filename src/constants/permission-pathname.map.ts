import type { Permission } from "@/types/permission.enum";
import PermissionEnum from "@/types/permission.enum";

export const pathnamePermissionMap: Record<string, Permission> = {
  "/": PermissionEnum.Dashboard,

  "/system-settings": PermissionEnum.SystemSettings,
  "/system-settings/account": PermissionEnum.UserGetList,
  "/system-settings/member": PermissionEnum.UserGetList,

  "/system-settings/role": PermissionEnum.RoleGetList,
  "/system-settings/role/new": PermissionEnum.RoleCreate,
  "/system-settings/role/:id/edit": PermissionEnum.RoleEdit,

  "/system-settings/analysis": PermissionEnum.AnalysisList,
} as const;
