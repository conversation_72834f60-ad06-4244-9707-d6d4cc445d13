# Windsurf Rules for Bonito Frontend (Vite)
# Social Media Monitoring & Content Analysis Platform

## Project Overview
This is a React + TypeScript + Vite project for a social media intelligence platform that monitors, analyzes, and provides insights on online content across multiple platforms including forums, social media, and news outlets.

## Core Technologies & Dependencies
- React 19 with TypeScript
- Vite 7 for build tooling
- React Router 7 for navigation
- Tailwind CSS 4 + Radix UI for styling
- React Query (TanStack) for server state
- Zustand for global state management
- i18next for internationalization (en-US, zh-TW)
- React Hook Form + Zod for form validation
- Recharts for data visualization
- Axios for HTTP requests

## File Structure & Naming Conventions

### Directory Structure
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui base components
│   ├── dashboard/      # Dashboard-specific components
│   ├── topic-analysis/ # Feature-specific components
│   └── [feature]/      # Other feature-specific components
├── pages/              # Route-level page components
│   ├── dashboard/
│   ├── topic-analysis/
│   └── [feature]/
├── layouts/            # Layout components
├── hooks/              # Custom React hooks
├── lib/                # Utility functions and configurations
├── assets/             # Static assets
└── locales/            # i18n translation files
```

### Naming Conventions
- **Files**: Use kebab-case for directories, PascalCase for component files
- **Components**: PascalCase (e.g., `TopicAnalysis.tsx`, `MainDashboard.tsx`)
- **Hooks**: camelCase starting with "use" (e.g., `useAnalysisRecords.ts`)
- **Types**: PascalCase interfaces (e.g., `AnalysisRecord`, `SearchFormProps`)
- **Constants**: UPPER_SNAKE_CASE or camelCase objects

## Component Patterns

### Component Structure
```typescript
// Always use functional components with TypeScript
import type React from "react";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

interface ComponentProps {
  // Define props with TypeScript interfaces
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  const { t } = useTranslation("namespace");
  
  // State and hooks
  const [state, setState] = useState<Type>(initialValue);
  
  // Event handlers
  const handleAction = () => {
    // Implementation
  };
  
  return (
    <div className="tailwind-classes">
      {/* JSX content */}
    </div>
  );
}
```

### Re-export Pattern
- Use index.ts files to re-export components from directories
- Keep clean import paths with barrel exports

### Custom Hooks Pattern
```typescript
export function useCustomHook() {
  // State and logic
  const [state, setState] = useState();
  
  // Return object with clear naming
  return {
    state,
    setState,
    // Action functions
    handleAction,
  };
}
```

## Styling Guidelines

### Tailwind CSS Usage
- Use Tailwind utility classes exclusively
- Follow responsive design patterns: `sm:`, `md:`, `lg:`, `xl:`
- Use custom color palette: `primary`, `secondary` variants
- Consistent spacing using Tailwind scale

### Component Styling Patterns
```typescript
// Use clsx for conditional classes
import { clsx } from "clsx";

const buttonClasses = clsx(
  "base-classes",
  variant === "primary" && "primary-classes",
  disabled && "disabled-classes"
);
```

## State Management

### Local State
- Use `useState` for component-level state
- Use `useReducer` for complex state logic
- Custom hooks for reusable stateful logic

### Global State (Zustand)
```typescript
interface StoreState {
  data: DataType;
  actions: {
    updateData: (data: DataType) => void;
  };
}

export const useStore = create<StoreState>((set) => ({
  data: initialData,
  actions: {
    updateData: (data) => set({ data }),
  },
}));
```

### Server State (React Query)
```typescript
export function useAnalysisData(params: QueryParams) {
  return useQuery({
    queryKey: ['analysis', params],
    queryFn: () => fetchAnalysisData(params),
    enabled: !!params.query,
  });
}
```

## Routing Patterns

### Route Structure
- Use React Router 7 with createBrowserRouter
- Nest routes under main layout
- Page components in `/pages` directory
- Route-specific logic in page components

### Navigation
```typescript
// Prefer programmatic navigation
const navigate = useNavigate();
navigate('/path', { replace: true });

// Or direct window.location for external-like navigation
window.location.href = `/path?param=${value}`;
```

## Internationalization (i18n)

### Translation Usage
```typescript
const { t } = useTranslation("namespace");

// In JSX
<h1>{t("keyName")}</h1>
<p>{t("keyWithVariable", { variable: value })}</p>
```

### Translation File Structure
```
public/locales/
├── en-US/
│   ├── common.json
│   ├── dashboard.json
│   └── topicAnalysis.json
└── zh-TW/
    ├── common.json
    ├── dashboard.json
    └── topicAnalysis.json
```

## Form Handling

### React Hook Form + Zod Pattern
```typescript
const schema = z.object({
  field: z.string().min(1, "Required"),
});

type FormData = z.infer<typeof schema>;

const form = useForm<FormData>({
  resolver: zodResolver(schema),
  defaultValues: {},
});

const onSubmit = (data: FormData) => {
  // Handle form submission
};
```

## Data Visualization

### Recharts Usage
- Use Recharts for all data visualization
- Consistent color schemes matching design system
- Responsive chart containers
- Proper data formatting and error handling

## API Integration

### Axios Configuration
- Centralized API configuration
- Consistent error handling
- Request/response interceptors for common logic
- TypeScript interfaces for API responses

### Data Types
```typescript
interface AnalysisRecord {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
  status: "completed" | "analyzing" | "failed";
  sentiment: {
    positive: number;
    neutral: number;
    negative: number;
    goodwillScore: number;
  };
  // ... other fields
}
```

## Error Handling

### Component Error Boundaries
- Implement error boundaries for feature sections
- Graceful fallback UI components
- Error logging and reporting

### Async Error Handling
```typescript
try {
  const result = await apiCall();
  // Handle success
} catch (error) {
  // Handle error with proper typing
  console.error('Operation failed:', error);
}
```

## Performance Guidelines

### Component Optimization
- Use React.memo for expensive components
- Implement proper dependency arrays in useEffect
- Debounce search inputs and API calls
- Lazy load heavy components

### Bundle Optimization
- Dynamic imports for route-based code splitting
- Optimize asset loading
- Tree-shake unused dependencies

## Code Quality Rules

### TypeScript
- Strict TypeScript configuration
- Prefer interfaces over types for object shapes
- Use proper typing for all props, state, and API responses
- Avoid `any` type usage

### ESLint Configuration
- Follow configured ESLint rules
- Use Prettier for consistent formatting
- Enforce React hooks rules
- Import organization and sorting

### Testing Patterns
- Component testing with React Testing Library
- Unit tests for utility functions
- Integration tests for complex workflows
- E2E tests for critical user journeys

## Security Guidelines

### Data Handling
- Sanitize user inputs
- Proper validation on form submissions
- Secure API communication
- Handle sensitive data appropriately

### Content Security
- XSS prevention
- Proper URL encoding for search parameters
- Validate data from external sources

## Development Workflow

### Git Conventions
- Use conventional commit messages
- Feature branch workflow
- Code review requirements
- Automated CI/CD pipeline

### Environment Configuration
- Environment-specific configurations
- Proper secret management
- Development vs production builds

## Feature-Specific Guidelines

### Dashboard Components
- Consistent card layouts using Card component
- Responsive grid systems
- Loading states and error handling
- Real-time data updates

### Analysis Features
- Consistent search form patterns
- Results display with pagination
- Export functionality
- Filtering and sorting capabilities

### Monitoring Features
- Real-time data updates
- WebSocket integration patterns
- Notification systems
- Data refresh mechanisms

## Dependencies Management

### Core Dependencies
- Keep React, TypeScript, and Vite up to date
- Regular security updates
- Peer dependency compatibility
- Bundle size monitoring

### UI Components
- Prefer Radix UI primitives
- Consistent component API patterns
- Accessibility compliance
- Custom component documentation

This configuration ensures consistent development patterns, maintainable code structure, and optimal performance for the Bonito social media monitoring platform.
