# 1) Build stage
ARG NODE_VERSION=22-alpine
FROM node:${NODE_VERSION} AS builder

# Set staging mode so your code can pick it up (e.g. process.env.NODE_ENV)
ARG NODE_ENV=staging
ENV NODE_ENV=${NODE_ENV}

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# 2) Staging runtime stage
FROM nginx:stable-alpine AS staging

# Expose port 8080 for staging
EXPOSE 8090

# Pass in your API endpoint at build time, defaulting to the staging API
ARG VITE_API_ENDPOINT=https://staging.api.example.com
ENV VITE_API_ENDPOINT=${VITE_API_ENDPOINT}
ENV NODE_ENV=staging

# Copy built assets and custom Nginx config
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.staging.conf /etc/nginx/conf.d/default.conf

# Run Nginx in the foreground
CMD ["nginx", "-g", "daemon off;"]
