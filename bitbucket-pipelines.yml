image: atlassian/default-image:3

pipelines:
  branches:
    staging:
      - step:
          name: Deploy Bonito Frontend to Staging via SSH
          deployment: staging
          script:
            - echo "🔐 Connecting SSH and deploying Bonito Frontend to staging..."
            # SSH into server, pull latest code and restart Docker Compose
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $SSH_USER
                SERVER: $STAGING_SERVER
                COMMAND: |
                  set -e
                  cd ~/bonito-fe-csr
                  echo "➡️ Pull latest frontend code"
                  git pull origin staging

                  echo "🛑 Stop old containers"
                  docker compose -f docker-compose.staging.yml down || true
                    
                  echo "🚀 Build and restart frontend services"
                  docker compose -f docker-compose.staging.yml up --build -d

                  echo "🔍 Check container status"
                  sleep 15
                  docker compose -f docker-compose.staging.yml ps

                  echo "📊 Container logs (last 20 lines)"
                  docker compose -f docker-compose.staging.yml logs --tail=20 staging

          services:
            - docker
          caches:
            - docker

definitions:
  services:
    docker:
      memory: 2048
  caches:
    node: node_modules
