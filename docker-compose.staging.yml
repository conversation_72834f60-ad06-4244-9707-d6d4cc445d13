services:
  staging:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Can override with `--env-file` or host env VAR
        VITE_API_ENDPOINT: ${VITE_API_ENDPOINT:-https://staging.api.example.com}
        NODE_ENV: staging
    environment:
      - NODE_ENV=staging
      - VITE_API_ENDPOINT=${VITE_API_ENDPOINT}
    ports:
      - "8090:8090"
    # uncomment if you have a backend in the same compose
    # depends_on:
    #   - backend

  # example of adding a staging backend alongside
  # backend:
  #   image: my-backend:staging
  #   environment:
  #     - NODE_ENV=staging
  #   networks:
  #     - staging-net
# optional network isolation
# networks:
#   staging-net:
#     driver: bridge
